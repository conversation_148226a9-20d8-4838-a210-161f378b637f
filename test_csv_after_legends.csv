"Alert Name","Occurrences","Resolved_Same_Day","Priority","Has_Replies","Number_of_Replies","Description","Alert_Types_With_Replies","Max_Replies_On_Alert_Type","Service Owner"
"linux_NODE_LOW_REDIS_MEMORY ***********:9273 billpaymentsrechargesffrredisprod","8","true","P1","true","8","Node ***********:9273 of rechargesffr has had low memory over 5m - Value 11.985430109446575","3","6","Team Harish"
"linux_NODE_LOW_MEMORY **********:9273 reminder_cluster_new","1","false","P1","true","1","Instnace name 'prod-cassandra-reminderdb04' with IP **********:9273 of  has had low memory over 5m - Value 4.976458210238704","1","1","<PERSON> Arvind"
"rechargesbff_home_reminder_4xx_5xx_high","11","false","Normal","true","4","Recharges-BFF-Home-Reminder 4xx 5xx count is high 2.803738317757009","1","4","Team Arvind"
"digital_billpayments_node_disk_usage **********:9273 recharge_cassandra","7","false","P1","false","0","Disk usage &gt; 85% : **********:9273 /","0","0","No Owner"
"recharge-plan-service-client-servers-searchplans-API-alert-4XX","61","true","P1","true","5","RPS-searchplans-API 4XX Alert from RPS Client Server  is greater than threshold 10. Value of RPS-searchplans-API 4XX: 8","1","5","No Owner"
"digital_billpayments-notifyTxn-Count","1","false","P0","true","2","Count of Notify transaction decreased in RU. Current value is 4400","1","2","Team Priya"
"digital_billpayments_node_disk_usage ***********:9273 recharge_cassandra","7","false","P1","false","0","Disk usage &gt; 85% : ***********:9273 /","0","0","No Owner"
"digital_billpayments-4XX-dgtkt-CHENNAI digital-ticketing(metro)","2","true","P1","true","3","Count of 4XX error code  on api:  is more than threshold 10 in the 5 minutes. Value is 100","1","3","No Owner"
"rechargesbff_ct_api_segments_Null","2","true","P1","false","0","Recharges-BFF ct api send failed response with http status = 2XX, sending segment id = """" to cart, count is 8539","0","0","Team Harish"
