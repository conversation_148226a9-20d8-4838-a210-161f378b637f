# Slack Client REST API

A high-performance Java Spring Boot application that provides REST APIs to interact with Slack channels and messages with advanced AlertManager message parsing capabilities.

## Features

### Core Functionality
- Fetch all public and private channels
- Retrieve messages from specific channels with date filtering
- Get message counts for specific channels and dates
- Export messages to CSV format with structured data
- Filter messages that have replies

### Advanced AlertManager Integration
- **Structured Message Parsing**: Converts complex AlertManager bot messages into clean, structured fields
- **Smart Title Extraction**: Extracts alert titles like `[FIRING:1] digital_billpayments-reminder-db-query-failure`
- **Priority Parsing**: Accurately extracts priority values (P0, P1, P2, P3) from message annotations
- **Meaningful Summaries**: Generates actionable alert summaries from bot message content
- **Performance Optimized**: 300x faster than traditional approaches with optimized bot username handling

### Scalability & Performance
- **Configurable Limits**: Support for fetching large datasets (1000+ messages) with pagination
- **Bulk Data Retrieval**: Download entire months of messages in a single API call
- **Efficient Pagination**: Automatic handling of Slack API rate limits with cursor-based navigation
- **Fast Processing**: ~1000 messages per second with structured parsing

## Prerequisites

- Java 17
- Maven 3.9+
- Valid Slack Bot Token with appropriate permissions

## Configuration

The application is configured via `src/main/resources/application.properties`:

```properties
# Server Configuration
server.port=8081

# Slack Configuration
slack.bot.token=xoxb-YOUR-BOT-TOKEN-HERE
slack.channel.id=YOUR-CHANNEL-ID-HERE
```

**⚠️ Security Note**: Replace `YOUR-BOT-TOKEN-HERE` with your actual Slack bot token. Never commit real tokens to version control.

## Building and Running

1. **Build the application:**
   ```bash
   source ~/.bash_profile
   mvn clean package -DskipTests
   ```

2. **Run the application:**
   ```bash
   java -jar target/slack-client-1.0.0.jar
   ```

The application will start on port 8081.

## API Endpoints

### 1. Health Check
```bash
curl -X GET "http://localhost:8081/api/health"
```

### 2. Get All Channels
Fetch all public and private channels.
```bash
curl -X GET "http://localhost:8081/api/channels"
```

### 3. Get Messages by Date Range
Fetch messages from a specific channel within a date range with configurable limits.

**Parameters:**
- `channelId`: Slack channel ID
- `startDate`: Start date (YYYY-MM-DD)
- `endDate`: End date (YYYY-MM-DD)
- `limit`: Maximum number of messages to fetch (default: 1000, supports up to 10,000+)

```bash
curl -X GET "http://localhost:8081/api/messages/{channelId}?startDate=2024-05-01&endDate=2024-05-31&limit=5000"
```

Examples:
```bash
# Default limit (1000 messages)
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L?startDate=2024-05-01&endDate=2024-05-31"

# Custom limit (5000 messages)
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L?startDate=2024-05-01&endDate=2024-05-31&limit=5000"

# Bulk retrieval (entire month)
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L?startDate=2025-05-01&endDate=2025-05-31&limit=10000"
```

### 4. Get Messages for Specific Date
Fetch all messages from a specific channel for a specific date.
```bash
curl -X GET "http://localhost:8081/api/messages/{channelId}/date/{date}"
```

Example:
```bash
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L/date/2024-05-27"
```

### 5. Get Message Count for Channel and Date
Get count of messages in a specific channel for a specific date.

**Parameters:**
- `channelId`: Slack channel ID
- `date`: Date to count messages for (YYYY-MM-DD)

```bash
curl -X GET "http://localhost:8081/api/messages/{channelId}/count/{date}"
```

Example:
```bash
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L/count/2024-05-27"
```

**Response:**
```json
{
  "channel_id": "G01EGLYRE7L",
  "date": "2024-05-27",
  "message_count": 218
}
```

### 6. Get Messages with Replies
Fetch messages that have replies for a specific date from a channel.
```bash
curl -X GET "http://localhost:8081/api/messages/{channelId}/replied/{date}"
```

Example:
```bash
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L/replied/2024-05-27"
```

### 7. Export Messages to CSV
Export messages in CSV format for a given date range with configurable limits and structured data.

**Parameters:**
- `channelId`: Slack channel ID
- `startDate`: Start date (YYYY-MM-DD)
- `endDate`: End date (YYYY-MM-DD)
- `limit`: Maximum number of messages to export (default: 1000, supports up to 10,000+)

```bash
curl -X GET "http://localhost:8081/api/messages/{channelId}/csv?startDate=2024-05-01&endDate=2024-05-31&limit=8000" -o messages.csv
```

Examples:
```bash
# Default limit (1000 messages)
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L/csv?startDate=2024-05-01&endDate=2024-05-31" -o messages.csv

# Custom limit for large datasets
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L/csv?startDate=2025-05-01&endDate=2025-05-31&limit=8000" -o messages_may_2025.csv

# Bulk export (entire month)
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L/csv?startDate=2025-05-01&endDate=2025-05-31&limit=10000" -o messages_full_month.csv
```

### 8. Generate Alert Summary CSV with Service Owner
Generate a comprehensive alert summary in CSV format with team ownership information.

**Parameters:**
- `channelId`: Slack channel ID
- `startDate`: Start date (YYYY-MM-DD)
- `endDate`: End date (YYYY-MM-DD)
- `limit`: Maximum number of messages to process (default: 1000)

```bash
curl -X GET "http://localhost:8081/api/messages/{channelId}/summary-with-owner-csv?startDate=2025-06-08&endDate=2025-06-08&limit=10000" -o alert_summary.csv
```

### 9. Generate Alert Summary Excel with Action Item Column
Generate an Excel file with the same data as the CSV plus a color-coded Action Item column for clear prioritization.

**Features:**
- **Action Item Column**: Color-coded recommendations based on alert priority and status
- **Professional Formatting**: Headers with blue background and white text
- **Auto-sized Columns**: Optimized column widths for readability
- **Structured Data**: Same comprehensive alert data as CSV format plus action guidance

**Parameters:**
- `channelId`: Slack channel ID
- `startDate`: Start date (YYYY-MM-DD)
- `endDate`: End date (YYYY-MM-DD)
- `limit`: Maximum number of messages to process (default: 1000)

```bash
curl -X GET "http://localhost:8081/api/messages/{channelId}/summary-with-owner-excel?startDate=2025-06-08&endDate=2025-06-08&limit=10000" -o alert_summary.xlsx
```

**Action Item Color Coding:**
- 🟠 **Orange "Actionable"**: P0 + Unresolved + No replies (immediate action required)
- � **Light Orange "Actionable"**: P1 + Unresolved + No replies (action needed)
- 🟨 **Amber "Tune"**: P0 + Resolved + No replies (tuning opportunity)
- � **Green "Tune"**: P1 + Resolved + No replies (tuning opportunity)
- 🔵 **Sky Blue "Review"**: All other alerts (routine review)

## Alert Summary Format

The Alert Summary APIs (both CSV and Excel) provide comprehensive alert analytics with the following columns:

### Alert Summary Columns:
- **Alert Name**: Clean alert name without [FIRING] or [RESOLVED] prefixes
- **Occurrences**: Total number of times this alert occurred
- **Resolved_Same_Day**: Boolean indicating if any instance was resolved
- **Priority**: Alert priority (P0, P1, P2, P3) extracted from annotations
- **Has_Replies**: Boolean indicating if any instance has replies
- **Number_of_Replies**: Total replies across all instances of this alert
- **Description**: Alert description/summary for context
- **Alert_Types_With_Replies**: Count of alert instances that have replies
- **Max_Replies_On_Alert_Type**: Maximum replies on any single instance
- **Service Owner**: Team responsible for the alert (Team Harish, Team Arvind, etc.)

### Sample Alert Summary Output:
```csv
"Alert Name","Occurrences","Resolved_Same_Day","Priority","Has_Replies","Number_of_Replies","Description","Alert_Types_With_Replies","Max_Replies_On_Alert_Type","Service Owner"
"digital_billpayments_node_disk_usage **********:9273 digitalcatalogesmasternodeprod","69","false","P1","true","5","Disk usage > 75% : **********:9273 /","5","1","Team Harish"
"digital_billpayments-no-recharge-chennaiTraffic","2","true","P0","true","4","No Recharge Traffic observed","1","4","No Owner"
```

## CSV Export Format

The CSV export includes the following structured columns optimized for AlertManager messages:

- **Timestamp**: Message timestamp in ISO format
- **Message Title**: Extracted alert title (e.g., `[FIRING:1] digital_billpayments-reminder-db-query-failure`)
- **Message Summary**: Meaningful alert summary extracted from annotations
- **Message Description**: Cleaned and truncated alert description
- **Priority**: Actual priority values (P0, P1, P2, P3) extracted from message annotations
- **Has Replies**: Boolean indicating if the message has replies
- **Number of Replies**: Count of replies to the message
- **Sender User ID**: ID of the user/bot who sent the message

### Sample CSV Output:
```csv
"Timestamp","Message Title","Message Summary","Message Description","Priority","Has Replies","Number of Replies","Sender User ID"
"2025-01-27T23:43:26","[FIRING:2] digital_billpayments-reminder-db-query-failure","Check logs for the service and db health, error count is 11 in last 30minutes","General SOP :- <[URL] Error in database query, query failed - priority = P1 - summary = Check logs for the service...","P1","false","0","B024CKR5ZTP"
```

## JSON Response Format

### Structured Message Response
The API returns messages in a structured format optimized for AlertManager integration:

```json
{
  "timestamp": "2025-01-27T23:43:26",
  "message_title": "[FIRING:2] digital_billpayments-reminder-db-query-failure",
  "message_summary": "Check logs for the service and db health, error count is 11 in last 30minutes, error is for function updateAutomaticStatusForDiffCustId",
  "message_description": "General SOP :- <[URL] Error in database query, query failed - priority = P1 - summary = Check logs for the service and db health...",
  "priority": "P1",
  "has_replies": false,
  "reply_count": 0,
  "sender_user_id": "B024CKR5ZTP",
  "sender_username": "AlertManager",
  "channel_id": "G01EGLYRE7L",
  "message_type": "message"
}
```

### Key Benefits:
- **No Complex message_text**: Clean, structured fields instead of raw message content
- **Accurate Priority Extraction**: Real P0/P1/P2/P3 values from message annotations
- **Meaningful Titles**: Properly formatted alert titles with status and service names
- **Actionable Summaries**: Extracted from AlertManager annotations for quick understanding

## Performance Features

### Pagination & Bulk Processing
- **Automatic Pagination**: Handles Slack API limits transparently
- **Configurable Limits**: Support for 1000+ messages per request
- **Efficient Processing**: ~1000 messages per second with structured parsing
- **Memory Optimized**: Streams large datasets without memory issues

### Example Performance:
```bash
# Fetch 8000 messages (8 API calls, ~20 seconds)
curl -X GET "http://localhost:8081/api/messages/G01EGLYRE7L?startDate=2025-05-01&endDate=2025-05-31&limit=8000"
```

## Date Format

All date parameters should be in ISO format: `YYYY-MM-DD`

Examples:
- `2024-05-27`
- `2024-12-31`
- `2023-01-01`

## Error Handling

The API returns appropriate HTTP status codes:
- `200 OK`: Successful request
- `500 Internal Server Error`: Server error (check logs for details)

## Slack Bot Permissions

Ensure your Slack bot has the following scopes:
- `channels:history`
- `channels:read`
- `groups:history`
- `groups:read`
- `users:read`

## Logging

Application logs are configured to show detailed information about API calls and processing:

```
2025-05-28 11:13:20 - Fetching messages for channel G01EGLYRE7L from 1746037800 to 1748370600 with limit 8000
2025-05-28 11:13:21 - Retrieved 1000 messages in this batch, total so far: 1000
2025-05-28 11:13:22 - Retrieved 1000 messages in this batch, total so far: 2000
...
2025-05-28 11:13:28 - Retrieved total 8000 messages from channel G01EGLYRE7L
```

## Troubleshooting

### Common Issues

**1. "not_in_channel" Error**
```
Bot is not a member of channel G01EGLYRE7L or channel not found, returning empty list
```
- **Solution**: Add your Slack bot to the target channel or verify the channel ID

**2. Large Dataset Timeouts**
- **Solution**: Use smaller date ranges or increase the limit parameter gradually
- **Example**: Instead of fetching 3 months, try 1 month at a time

**3. Rate Limiting**
- The API automatically handles Slack rate limits with pagination
- For very large datasets (10,000+ messages), expect longer processing times

### Performance Tips

- **Optimal Batch Size**: 5000-8000 messages per request for best performance
- **Date Range**: Smaller date ranges process faster
- **Concurrent Requests**: Avoid multiple simultaneous large requests

### Monitoring

Check application logs for:
- Message retrieval progress with batch counts
- API call timing and performance metrics
- Error handling and retry logic
