"Alert Name","Occurrences","Resolved_Same_Day","Priority","Has_Replies","Number_of_Replies","Description","Alert_Types_With_Replies","Max_Replies_On_Alert_Type"
"linux_NODE_LOW_REDIS_MEMORY ***********:9273 billpaymentsrechargesffrredisprod","11","true","P1","true","3","Node ***********:9273 of rechargesffr has had low memory over 5m - Value 10.037158484830524","26","5"
"digital_billpayments-notifyTxn-Count","5","true","P0","true","6","Count of Notify transaction decreased in RU. Current value is 4396","26","5"
"digital_billpayments_node_disk_usage **********:9273 digitalcatalogesmasternodeprod","69","false","P1","true","5","Disk usage &gt; 75% : **********:9273 /","26","5"
"digital_billpayments-Saga-Plan-Validity-Consumer-lag","8","true","P1","true","1","Lag in saga Plan Validity consumer for more than 10m. Value is 1015.0208333333334 on topic:","26","5"
"linux_NODE_HIGH_CPU ***********:9273 billpaymentsru-elkelasticsearchprod","16","true","P1","true","2","Instance ***********:9273 of ru-elk has had high CPU usage Value 85.00 for over 15m","26","5"
"digital_billpayments-4XX-dgtkt-CHENNAI digital-ticketing(metro)","2","true","P1","true","2","Count of 4XX error code  on api:  is more than threshold 10 in the 5 minutes. Value is 103","26","5"
"rechargesbff_ct_api_segments_Null","5","true","P1","true","2","Recharges-BFF ct api send failed response with http status = 2XX, sending segment id = """" to cart, count is 8555","26","5"
"digital_billpayments-Saga-OMS-Consumer-lag-alerts","7","true","P1","true","3","Lag in saga oms consumer for more than 10m. Value is 1022.4609374999999 on topic:","26","5"
"digital_billpayments-no-recharge-chennaiTraffic","2","true","P0","true","4","No Recharge Traffic observed on Challan Top Operator: chennai traffic police for the last 1 hr","26","5"
"digital-catalog-client-APIs-success-rate-day","2","true","P1","false","0","DCAT client APIs Success Ratio during Day is less than 0.95 in last 1 minute. Value of success ratio: 0.9208228150371836","26","5"
"digital-catalog-client-getSeoDataAPI-4XX","10","true","P1","true","3","Get SeoData 4XX count is greater than threshold 220. Value of 4XX: 2753","26","5"
"digital_billpayments-Saga-Reminder-Consumer-lag","7","true","P1","true","1","Lag in saga Reminder consumer for more than 10m. Value is 4123.763888888889 on topic:","26","5"
"linux_NODE_HIGH_CPU 10.4.32.233:9273 billpaymentsru-elkelasticsearchprod","13","true","P1","true","1","Instance 10.4.32.233:9273 of ru-elk has had high CPU usage Value 80.02 for over 15m","26","5"
"Alert Notification","5","false","Normal","true","1","Alert summary not available","26","5"
"digital_billpayments-PDN-CALLBACK-API-SUCCESS-rate-drop","8","true","P1","true","2","PDN Callback API Success rate drop to less than 85%. Instructions for on-call:- Please monitor the graph pattern with earlier days. Value is 83.14049586776859","26","5"
"linux_NODE_HIGH_CPU 10.4.35.115:9273 billpaymentsgvtn-sagaconsumer-cache-cleanspringbootprod","2","true","P1","false","0","Instance 10.4.35.115:9273 of gvtn-sagaconsumer-cache-clean has had high CPU usage Value 75.34 for over 15m","26","5"
"SMS_Partial_To_Full_Low_Traffic_Mobile_PrePaid","16","true","P1","true","6","SMS Parsing Partial To Full Low Traffic As Compare to yesterday","26","5"
"linux_NODE_HIGH_CPU 10.4.32.10:9273 billpaymentsru-elkelasticsearchprod","2","true","P1","true","1","Instance 10.4.32.10:9273 of ru-elk has had high CPU usage Value 80.22 for over 15m","26","5"
"digital_billpayments-reminder-common-or-vil-pv-notification-kafka-lag","2","true","P1","false","0","Consumer Lag for topic COMMON_PV_NOTIFICATION exists. Value is 109536","26","5"
"CPU_HIGH_LOAD_5MIN ***********:9273 billpaymentsru-elkelasticsearchprod","8","true","P1","false","0","Instance ***********:9273 of ru-elk had high cpu load for over 5m - Value 1.515","26","5"
"digital_billpayments-reminder-notificationService_7-consumer-kafka-lag","5","true","P1","false","0","Queue is not fully drained yet. Either draining rate may be slow or queue under high load.  Consumer Lag for topic NOTIFICATION_7 exists. Value is 150569","26","5"
"digital_billpayments-reminder-common-pv-consumer","4","true","P1","false","0","Service is planValiditySubscriber, output is dropped, either latencies have increased for the service or rejection has increased, count is 363","26","5"
"digital_billpayments-UPI-disabled-paymode-P1","3","true","P1","true","5","UPI Disabled Paymode High Count per Category","26","5"
"CPU_HIGH_LOAD_5MIN 10.4.32.233:9273 billpaymentsru-elkelasticsearchprod","12","true","P1","false","0","Instance 10.4.32.233:9273 of ru-elk had high cpu load for over 5m - Value 1.5025","26","5"
"digital_billpayments-RENT-Biller-Service-validateBiller-API-Account-Success-Rate-Drop-Bank-P0","8","true","P0","true","11","Biller Service validateBiller API Success Rate for Account is lower than threshold 60 in last 5 minutes. Current value is 47.***************","26","5"
"digital_billpayments-highLatency-dgtkt-delhi--mainline-api digital-ticketing(metro)","2","true","P2","false","0","Latency of DelhiMetro Mainline API: /v1/mumbaimetro/price is more than threshold 20000 ms in the last 5m. Value is 25077.************","26","5"
"CPU_HIGH_LOAD_5MIN 10.4.33.117:9273 billpaymentssmartreminderaerospikeprod","4","true","P1","true","3","Instance 10.4.33.117:9273 of smartreminder had high cpu load for over 5m - Value 1.50375","26","5"
"CPU_HIGH_LOAD_5MIN 10.4.33.165:9273 billpaymentssmartreminderaerospikeprod","4","true","P1","false","0","Instance 10.4.33.165:9273 of smartreminder had high cpu load for over 5m - Value 1.5525","26","5"
"CPU_HIGH_LOAD_5MIN 10.4.33.248:9273 billpaymentssmartreminderaerospikeprod","4","true","P1","false","0","Instance 10.4.33.248:9273 of smartreminder had high cpu load for over 5m - Value 1.58","26","5"
"linux_NODE_HIGH_CPU 10.4.33.117:9273 billpaymentssmartreminderaerospikeprod","3","true","P1","false","0","Instance 10.4.33.117:9273 of smartreminder has had high CPU usage Value 86.10 for over 15m","26","5"
"linux_NODE_HIGH_CPU 10.4.33.165:9273 billpaymentssmartreminderaerospikeprod","3","true","P1","false","0","Instance 10.4.33.165:9273 of smartreminder has had high CPU usage Value 85.25 for over 15m","26","5"
"linux_NODE_HIGH_CPU 10.4.33.248:9273 billpaymentssmartreminderaerospikeprod","3","true","P1","false","0","Instance 10.4.33.248:9273 of smartreminder has had high CPU usage Value 85.37 for over 15m","26","5"
"digital_billpayments-auxiliarykafka-consumer-Lag digital-billpayments-auxiliarykafka","10","true","P1","true","7","Kafka cluster digital-billpayments-auxiliarykafka Consumer Lag for topic REMINDER_MAXWELL exists. Value is 6563613.00","26","5"
"digital_billpayments-bill-sync-pdn-failure-count","13","true","P1","true","2","High PDN Failure Count in the last 30 mins with threshold 200 in bill sync consumer. Value is 205","26","5"
"digital-catalog-client-APIs-success-rate-night","12","true","P1","true","3","DCAT client APIs Success Ratio during Night is less than 0.90 in last 1 minute. Value of success ratio: 0.****************","26","5"
"digital_billpayments-reminder-SMSParsing-consumer","2","true","P1","true","2","Only 2 consumers are running.","26","5"
"digitaldb_mysql_replication_lag_test **********:9273 digital_subscription","2","true","Normal","false","0","Alert summary not available","26","5"
"digital_billpayments-RENT-Biller-Service-validateBiller-API-Account-Success-Rate-Drop-UPI-P0","2","true","P0","true","2","Biller Service validateBiller API Success Rate for Account is lower than threshold 60 in last 5 minutes. Current value is 50","26","5"
"digital_billpayments-CheckoutNotify-Ratio","9","true","P2","false","0","Checkout requests coming on Bff to recieved on Notify at FFR has a drop . Value is 30.***************","26","5"
"digital_billpayments-reminder-recents-consumer-traffic-drop","3","true","P2","false","0","recent traffic dropped by 14282 in last 1h","26","5"
"digital_billpayments-HighLatency-Blue","11","true","P1","true","2","Validation API Latency is more than threshold 1 minute. Value is 6712.************ on gvtn-ffrbluenode-billpayments-v1-35-153. Raise to the monitoring team to check the latency at the operators end.","26","5"
"digital_billpayments-settlement-wallet-balance-insufficient-balance","2","true","P0","true","2","PLEASE CONNECT ON FOLLOWING MOBILE NUMBER IMMEDIATELY(**********) OR FINANCE TEAM","26","5"
