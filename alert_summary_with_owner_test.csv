"Alert Name","Occurrences","Resolved_Same_Day","Priority","Has_Replies","Number_of_Replies","Description","Alert_Types_With_Replies","Max_Replies_On_Alert_Type","Service Owner"
"digital_billpayments-Saga-OMS-Consumer-lag-alerts","13","false","P1","true","3","Lag in saga oms consumer for more than 10m. Value is 5757.078125000004 on topic:","1","3","Team Harish"
"linux_NODE_LOW_REDIS_MEMORY ***********:9273 billpaymentsrechargesffrredisprod","16","true","P1","true","5","Node ***********:9273 of rechargesffr has had low memory over 5m - Value 6.816247559302223","1","5","Team Harish"
"linux_NODE_HIGH_CPU **********:9273 billpaymentsru-elkelasticsearchprod","9","true","P1","true","1","Instance **********:9273 of ru-elk has had high CPU usage Value 84.08 for over 15m","1","1","Team Pankaj"
"digital_billpayments_saga_saved_card_api_count","15","true","P1","true","9","Saga Saved API Non 2XX count increased. current value is 1.3162208234985575","1","9","Team Priya"
"digital_billpayments-Saga-Reminder-Consumer-lag","10","true","P1","false","0","Lag in saga Reminder consumer for more than 10m. Value is 1187.0902777777778 on topic:","0","0","Team Arvind"
"digital_billpayments-euronetpostpaidreminder-zero-success","4","true","P0","true","2","No Success Reminder Bill Fetches from last 5 minutes. Failure Rate Current value is 99.91503522497965","1","2","Team Arvind"
"digital_billpayments-Saga-cdc-recovery-consumer-DB-insert-reattempt-Error","2","true","P1","false","0","Saga cdc recovery consumer reattempting . Value is 725","0","0","Team Harish"
"CPU_HIGH_LOAD_5MIN 10.4.33.126:9273 billpaymentsupmskafkaprod","12","true","P1","false","0","Instance 10.4.33.126:9273 of upms had high cpu load for over 5m - Value 1.525","0","0","Team Priya"
"rechargesbff_ct_api_segments_Null","4","true","P1","true","3","Recharges-BFF ct api send failed response with http status = 2XX, sending segment id = """" to cart, count is 8519","1","3","Team Harish"
"digital_billpayments-4XX-dgtkt-CHENNAI digital-ticketing(metro)","2","true","P1","false","0","Count of 4XX error code  on api:  is more than threshold 10 in the 5 minutes. Value is 53","0","0","No Owner"
"linux_cpu_steal_high_Dwh-fulfillment-mirror 10.4.33.188:9273 billpaymentsDwh-fulfillment-mirrorkafkaprod","8","true","P1","false","0","average cpu steal has been above 15 for 10.4.33.188:9273 of billpaymentsDwh-fulfillment-mirrorkafkaprod and Dwh-fulfillment-mirror for 10 minutes. Last Value 15.066469361187208","0","0","Team Harish"
"digital_billpayments-reminder-notificationService_20-consumer-kafka-lag","2","true","P1","false","0","Queue is not fully drained yet. Either draining rate may be slow or queue under high load.  Consumer Lag for topic NOTIFICATION_20 exists. Value is 193107","0","0","Team Arvind"
"digital-catalog-client-getSeoDataAPI-4XX","3","true","P1","true","1","Get SeoData 4XX count is greater than threshold 220. Value of 4XX: 282","1","1","Team Harish"
