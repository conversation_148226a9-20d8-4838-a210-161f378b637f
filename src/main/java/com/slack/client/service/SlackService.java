package com.slack.client.service;

import com.slack.api.methods.MethodsClient;
import com.slack.api.methods.request.conversations.ConversationsHistoryRequest;
import com.slack.api.methods.request.conversations.ConversationsListRequest;
import com.slack.api.methods.response.conversations.ConversationsHistoryResponse;
import com.slack.api.methods.response.conversations.ConversationsListResponse;
import com.slack.api.model.Conversation;
import com.slack.api.model.Message;
import com.slack.client.config.InstanceOwnerConfig;
import com.slack.client.model.ChannelInfo;
import com.slack.client.model.MessageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class SlackService {

    private static final Logger logger = LoggerFactory.getLogger(SlackService.class);

    // Inner class to hold parsed alert message parts
    private static class AlertMessageParts {
        String title;
        String summary;
        String description;
        String priority;

        AlertMessageParts(String title, String summary, String description, String priority) {
            this.title = title;
            this.summary = summary;
            this.description = description;
            this.priority = priority;
        }
    }

    @Autowired
    private MethodsClient slackClient;

    @Autowired
    private InstanceOwnerConfig instanceOwnerConfig;

    public List<ChannelInfo> getAllChannels() {
        try {
            ConversationsListRequest request = ConversationsListRequest.builder()
                    .types(List.of(
                        com.slack.api.model.ConversationType.PUBLIC_CHANNEL,
                        com.slack.api.model.ConversationType.PRIVATE_CHANNEL
                    ))
                    .limit(1000)
                    .build();

            ConversationsListResponse response = slackClient.conversationsList(request);

            if (!response.isOk()) {
                throw new RuntimeException("Failed to fetch channels: " + response.getError());
            }

            return response.getChannels().stream()
                    .map(this::convertToChannelInfo)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("Error fetching channels", e);
            throw new RuntimeException("Error fetching channels", e);
        }
    }

    public List<MessageResponse> getMessagesInDateRange(String channelId, String startDate, String endDate) {
        return getMessagesInDateRange(channelId, startDate, endDate, 1000); // Default limit
    }

    public List<MessageResponse> getMessagesInDateRange(String channelId, String startDate, String endDate, int limit) {
        try {
            long startTimestamp = convertDateToTimestamp(startDate);
            long endTimestamp = convertDateToTimestamp(endDate) + 86400; // Add 24 hours to include end date

            logger.info("Fetching messages for channel {} from {} to {} with limit {}", channelId, startTimestamp, endTimestamp, limit);

            List<MessageResponse> allMessages = new ArrayList<>();
            String cursor = null;
            int remainingLimit = limit;

            do {
                // Slack API has a maximum limit of 1000 per request
                int requestLimit = Math.min(remainingLimit, 1000);

                ConversationsHistoryRequest.ConversationsHistoryRequestBuilder requestBuilder = ConversationsHistoryRequest.builder()
                        .channel(channelId)
                        .oldest(String.valueOf(startTimestamp))
                        .latest(String.valueOf(endTimestamp))
                        .limit(requestLimit);

                if (cursor != null) {
                    requestBuilder.cursor(cursor);
                }

                ConversationsHistoryRequest request = requestBuilder.build();
                ConversationsHistoryResponse response = slackClient.conversationsHistory(request);

                if (!response.isOk()) {
                    String error = response.getError();
                    logger.warn("Failed to fetch messages from channel {}: {}", channelId, error);

                    // If bot is not in channel, return empty list instead of throwing exception
                    if ("not_in_channel".equals(error) || "channel_not_found".equals(error)) {
                        logger.info("Bot is not a member of channel {} or channel not found, returning empty list", channelId);
                        return new ArrayList<>();
                    }

                    throw new RuntimeException("Failed to fetch messages: " + error);
                }

                List<MessageResponse> batchMessages = response.getMessages().stream()
                        .map(message -> convertToMessageResponse(message, channelId))
                        .collect(Collectors.toList());

                allMessages.addAll(batchMessages);
                remainingLimit -= batchMessages.size();

                // Update cursor for next iteration
                cursor = response.getResponseMetadata() != null ? response.getResponseMetadata().getNextCursor() : null;

                logger.debug("Retrieved {} messages in this batch, total so far: {}", batchMessages.size(), allMessages.size());

                // Continue if we have more messages to fetch and haven't reached the limit
            } while (cursor != null && !cursor.isEmpty() && remainingLimit > 0);

            logger.info("Retrieved total {} messages from channel {}", allMessages.size(), channelId);

            return allMessages;

        } catch (Exception e) {
            logger.error("Error fetching messages for date range from channel {}", channelId, e);
            throw new RuntimeException("Error fetching messages for date range", e);
        }
    }

    public List<MessageResponse> getMessagesForDate(String channelId, String date) {
        return getMessagesInDateRange(channelId, date, date);
    }

    public long getMessageCountForChannelAndDate(String channelId, String date) {
        try {
            List<MessageResponse> messages = getMessagesForDate(channelId, date);
            return messages.size();
        } catch (Exception e) {
            logger.error("Error counting messages for channel {} and date {}", channelId, date, e);
            throw new RuntimeException("Error counting messages for channel and date", e);
        }
    }

    public List<MessageResponse> getMessagesWithReplies(String channelId, String date) {
        List<MessageResponse> allMessages = getMessagesForDate(channelId, date);
        return allMessages.stream()
                .filter(MessageResponse::isHasReplies)
                .collect(Collectors.toList());
    }

    private ChannelInfo convertToChannelInfo(Conversation conversation) {
        // Use basic properties that are available
        String name = conversation.getName() != null ? conversation.getName() : conversation.getId();

        // For now, use simple defaults - we can enhance this later
        // The main functionality will work with ID and name
        return new ChannelInfo(
                conversation.getId(),
                name,
                false, // isPrivate - default to false
                true,  // isChannel - default to true
                false, // isGroup
                false, // isIm
                false, // isMpim
                0      // memberCount - default to 0
        );
    }

    private MessageResponse convertToMessageResponse(Message message, String channelId) {
        // Handle null values safely
        String messageText = extractMessageText(message);
        String userId = message.getUser() != null ? message.getUser() : null;
        String messageType = message.getType() != null ? message.getType() : "message";
        String timestamp = message.getTs() != null ? message.getTs() : "";

        // If user is null but we have botId or username, use those
        if (userId == null) {
            if (message.getBotId() != null) {
                userId = message.getBotId();
            } else if (message.getUsername() != null) {
                userId = message.getUsername();
            }
        }

        // Get username - optimized for performance (no API calls)
        String username = getOptimizedUsername(userId, message);

        // Check if message has replies
        boolean hasReplies = message.getReplyCount() != null && message.getReplyCount() > 0;
        int replyCount = message.getReplyCount() != null ? message.getReplyCount() : 0;

        // Parse the message text into structured fields
        AlertMessageParts parts = parseAlertMessage(messageText);

        return new MessageResponse(
                MessageResponse.formatSlackTimestamp(timestamp),
                parts.title,
                parts.summary,
                parts.description,
                parts.priority,
                hasReplies,
                replyCount,
                userId,
                username,
                channelId,
                messageType
        );
    }

    private String extractMessageText(Message message) {
        // First try the standard text field
        if (message.getText() != null && !message.getText().trim().isEmpty()) {
            return message.getText();
        }

        // For bot messages, try to extract from attachments
        if (message.getAttachments() != null && !message.getAttachments().isEmpty()) {
            StringBuilder textBuilder = new StringBuilder();
            for (var attachment : message.getAttachments()) {
                if (attachment.getText() != null && !attachment.getText().trim().isEmpty()) {
                    textBuilder.append(attachment.getText()).append(" ");
                }
                if (attachment.getTitle() != null && !attachment.getTitle().trim().isEmpty()) {
                    textBuilder.append("[Title: ").append(attachment.getTitle()).append("] ");
                }
                if (attachment.getPretext() != null && !attachment.getPretext().trim().isEmpty()) {
                    textBuilder.append(attachment.getPretext()).append(" ");
                }
                if (attachment.getFallback() != null && !attachment.getFallback().trim().isEmpty()) {
                    textBuilder.append(attachment.getFallback()).append(" ");
                }
            }
            String attachmentText = textBuilder.toString().trim();
            if (!attachmentText.isEmpty()) {
                return attachmentText;
            }
        }

        // For bot messages, try to extract from blocks (newer Slack format)
        if (message.getBlocks() != null && !message.getBlocks().isEmpty()) {
            StringBuilder textBuilder = new StringBuilder();
            for (var block : message.getBlocks()) {
                String blockText = extractTextFromBlock(block);
                if (blockText != null && !blockText.isEmpty()) {
                    textBuilder.append(blockText).append(" ");
                }
            }
            String blockText = textBuilder.toString().trim();
            if (!blockText.isEmpty()) {
                return blockText;
            }
        }

        // Create a meaningful summary for bot messages
        return createBotMessageSummary(message);
    }

    private String createBotMessageSummary(Message message) {
        StringBuilder summary = new StringBuilder();

        // Add bot information
        if (message.getUsername() != null) {
            summary.append("[").append(message.getUsername()).append(" Bot] ");
        } else if (message.getBotId() != null) {
            summary.append("[Bot ").append(message.getBotId()).append("] ");
        } else {
            summary.append("[System Message] ");
        }

        // Add timestamp info
        if (message.getTs() != null) {
            summary.append("Message at ").append(MessageResponse.formatSlackTimestamp(message.getTs()));
        }

        // Add subtype information if available
        if (message.getSubtype() != null) {
            summary.append(" (").append(message.getSubtype()).append(")");
        }

        // Add file information if present
        if (message.getFiles() != null && !message.getFiles().isEmpty()) {
            summary.append(" - Contains ").append(message.getFiles().size()).append(" file(s)");
        }

        // Add reaction information if present
        if (message.getReactions() != null && !message.getReactions().isEmpty()) {
            summary.append(" - Has ").append(message.getReactions().size()).append(" reaction(s)");
        }

        String result = summary.toString().trim();
        return result.isEmpty() ? "[Empty bot message]" : result;
    }

    private String extractTextFromBlock(Object block) {
        // This is a simplified implementation
        // In a real implementation, you'd need to parse the block structure properly
        if (block != null) {
            String blockStr = block.toString();
            // Try to extract any text content from the block string representation
            if (blockStr.contains("text")) {
                // This is a very basic extraction - you'd want to properly parse JSON here
                return "[Block content]";
            }
        }
        return null;
    }

    private String getOptimizedUsername(String userId, Message message) {
        if (userId == null) return "Unknown";

        // If it's a bot, use the bot username directly if available, otherwise use bot ID
        if (message.getUsername() != null) {
            return message.getUsername();
        }

        // For bot IDs, just return the bot ID to avoid slow API calls
        if (userId.startsWith("B0")) { // Bot IDs typically start with B0
            return userId; // Return bot ID as username to avoid slow API calls
        }

        // For real users, we could make API calls, but for performance, just return user ID
        return userId; // Return user ID for performance
    }

    /**
     * Parse AlertManager message text into structured components
     */
    private AlertMessageParts parseAlertMessage(String messageText) {
        if (messageText == null || messageText.trim().isEmpty()) {
            return new AlertMessageParts("Unknown Alert", "No summary available", "No description available", "Normal");
        }

        String title = extractTitle(messageText);
        String summary = extractSummary(messageText);
        String description = extractDescription(messageText);
        String priority = extractPriority(messageText);

        return new AlertMessageParts(title, summary, description, priority);
    }

    private String extractTitle(String messageText) {
        // Look for title in the format: [Title: [FIRING:1] alert-name || ...]
        Pattern titlePattern = Pattern.compile("\\[Title: (\\[(FIRING|RESOLVED|WARNING)[^\\]]*\\]\\s*[^\\|]+)");
        Matcher matcher = titlePattern.matcher(messageText);

        if (matcher.find()) {
            String title = matcher.group(1).trim();
            return title;
        }

        // Fallback: Look for alert patterns at the end of the message
        Pattern endAlertPattern = Pattern.compile("\\[(FIRING|RESOLVED|WARNING)[^\\]]*\\]\\s*([^\\|\\n]+)(?:\\s*\\|\\|.*)?$");
        matcher = endAlertPattern.matcher(messageText);

        if (matcher.find()) {
            String fullMatch = matcher.group(0).trim();
            // Remove everything after ||
            String cleanTitle = fullMatch.replaceAll("\\s*\\|\\|.*", "").trim();
            return cleanTitle;
        }

        // Another fallback: Look for any alert pattern in the message
        Pattern anyAlertPattern = Pattern.compile("\\[(FIRING|RESOLVED|WARNING)[^\\]]*\\]\\s*([a-zA-Z0-9_-]+(?:-[a-zA-Z0-9_-]+)*)");
        matcher = anyAlertPattern.matcher(messageText);

        if (matcher.find()) {
            return matcher.group(0).trim();
        }

        // Final fallback: extract from techteam and severity
        String techteam = extractField(messageText, "techteam");
        String severity = extractField(messageText, "severity");

        if (!techteam.isEmpty() && !severity.isEmpty()) {
            return techteam + " - " + severity + " alert";
        }

        return "Alert Notification";
    }

    private String extractSummary(String messageText) {
        // Look for summary in annotations
        Pattern summaryPattern = Pattern.compile("- summary = ([^\\n]+)");
        Matcher matcher = summaryPattern.matcher(messageText);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        // Fallback: use first line of description if available
        String description = extractField(messageText, "description");
        if (!description.isEmpty()) {
            String[] lines = description.split("\\.");
            if (lines.length > 0) {
                return lines[0].trim();
            }
        }

        return "Alert summary not available";
    }

    private String extractDescription(String messageText) {
        // Look for description in annotations
        Pattern descPattern = Pattern.compile("- description = ([^\\n]+(?:\\n[^-][^\\n]*)*)");
        Matcher matcher = descPattern.matcher(messageText);

        if (matcher.find()) {
            String desc = matcher.group(1).trim();
            // Clean up the description by removing URLs and extra formatting
            desc = desc.replaceAll("https?://[^\\s]+", "[URL]");
            desc = desc.replaceAll("\\s+", " ");
            return desc.length() > 200 ? desc.substring(0, 200) + "..." : desc;
        }

        // Fallback: extract labels info
        String techteam = extractField(messageText, "techteam");
        String severity = extractField(messageText, "severity");

        if (!techteam.isEmpty() || !severity.isEmpty()) {
            return String.format("Alert from team: %s, severity: %s",
                                techteam.isEmpty() ? "unknown" : techteam,
                                severity.isEmpty() ? "unknown" : severity);
        }

        return "Alert description not available";
    }

    private String extractPriority(String messageText) {
        // Look for priority in annotations first (- priority = P1/P2/P3)
        Pattern priorityPattern = Pattern.compile("- priority = (P[0-9]+)");
        Matcher matcher = priorityPattern.matcher(messageText);

        if (matcher.find()) {
            String priority = matcher.group(1).trim().toUpperCase();
            return priority; // Return the actual priority value (P0, P1, P2, P3)
        }

        // Look for priority patterns anywhere in the message (P0, P1, P2, P3)
        Pattern anyPriorityPattern = Pattern.compile("\\b(P[0-3])\\b");
        matcher = anyPriorityPattern.matcher(messageText);

        if (matcher.find()) {
            String priority = matcher.group(1).toUpperCase();
            return priority; // Return the actual priority value (P0, P1, P2, P3)
        }

        // Fallback: determine from severity and return a descriptive value
        String severity = extractField(messageText, "severity");
        switch (severity.toLowerCase()) {
            case "critical": return "Critical";
            case "warning": return "Warning";
            case "error": return "Error";
            default: return "Normal";
        }
    }

    private String extractField(String messageText, String fieldName) {
        Pattern pattern = Pattern.compile(fieldName + ":\\s*([^|\\n]+)");
        Matcher matcher = pattern.matcher(messageText);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        return "";
    }

    public String generateAlertSummaryCSV(String channelId, String startDate, String endDate, int limit) {
        try {
            // Get messages using existing method
            List<MessageResponse> messages = getMessagesInDateRange(channelId, startDate, endDate, limit);

            // Generate alert summary using the existing logic
            return generateAlertSummaryFromMessages(messages);

        } catch (Exception e) {
            logger.error("Error generating alert summary for channel {} from {} to {}", channelId, startDate, endDate, e);
            throw new RuntimeException("Error generating alert summary", e);
        }
    }

    private String generateAlertSummaryFromMessages(List<MessageResponse> messages) {
        Map<String, AlertInfo> alertMap = new LinkedHashMap<>();

        // Process each message
        for (MessageResponse message : messages) {
            String messageTitle = message.getMessageTitle();
            String priority = message.getPriority();
            boolean hasReplies = message.isHasReplies();
            int numReplies = message.getReplyCount();
            String summary = message.getMessageSummary();

            String alertName = extractAlertNameFromTitle(messageTitle);
            boolean isResolved = messageTitle.contains("[RESOLVED]");

            AlertInfo info = alertMap.getOrDefault(alertName, new AlertInfo());
            info.alertName = alertName;
            info.occurrences++;
            info.priority = info.priority == null ? priority : info.priority;
            info.description = info.description == null ? summary : info.description;
            info.resolved = info.resolved || isResolved;
            info.hasReplies = info.hasReplies || hasReplies;
            info.totalReplies += numReplies;

            alertMap.put(alertName, info);
        }

        // Generate CSV content
        StringBuilder csvContent = new StringBuilder();

        // Add header
        csvContent.append("\"Alert Name\",\"Occurrences\",\"Resolved_Same_Day\",\"Priority\",")
                  .append("\"Has_Replies\",\"Number_of_Replies\",\"Description\",")
                  .append("\"Alert_Types_With_Replies\",\"Max_Replies_On_Alert_Type\"\n");

        // Add data rows
        for (AlertInfo info : alertMap.values()) {
            csvContent.append("\"").append(escapeCSV(info.alertName)).append("\",")
                      .append("\"").append(info.occurrences).append("\",")
                      .append("\"").append(info.resolved).append("\",")
                      .append("\"").append(escapeCSV(info.priority)).append("\",")
                      .append("\"").append(info.hasReplies).append("\",")
                      .append("\"").append(info.totalReplies).append("\",")
                      .append("\"").append(escapeCSV(info.description)).append("\",")
                      .append("\"").append(info.hasReplies ? 1 : 0).append("\",")
                      .append("\"").append(info.totalReplies).append("\"\n");
        }

        return csvContent.toString();
    }

    private String extractAlertNameFromTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "Unknown Alert";
        }

        String cleanTitle = title.trim();

        // Remove any leading/trailing quotes if present
        if (cleanTitle.startsWith("\"") && cleanTitle.endsWith("\"")) {
            cleanTitle = cleanTitle.substring(1, cleanTitle.length() - 1);
        }

        // Extract the base alert name by removing [FIRING:X] or [RESOLVED] prefixes
        // This allows proper grouping while maintaining accurate counts
        Pattern pattern = Pattern.compile("^\\[(FIRING:\\d+|RESOLVED)\\]\\s*(.+)$");
        Matcher matcher = pattern.matcher(cleanTitle);

        if (matcher.find()) {
            return matcher.group(2).trim();
        }

        // If no pattern matches, return the clean title
        return cleanTitle;
    }

    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        // Escape quotes by doubling them
        return value.replace("\"", "\"\"");
    }

    // Inner class for alert information (same as in AlertSummaryGenerator)
    private static class AlertInfo {
        String alertName;
        String priority;
        String description;
        int occurrences = 0;
        boolean resolved = false;
        boolean hasReplies = false;
        int totalReplies = 0;
    }

    public String generateAlertSummaryWithOwnerCSV(String channelId, String startDate, String endDate, int limit) {
        try {
            // Get messages using existing method
            List<MessageResponse> messages = getMessagesInDateRange(channelId, startDate, endDate, limit);

            // Generate alert summary with service owner using the existing logic
            return generateAlertSummaryWithOwnerFromMessages(messages);

        } catch (Exception e) {
            logger.error("Error generating alert summary with owner for channel {} from {} to {}", channelId, startDate, endDate, e);
            throw new RuntimeException("Error generating alert summary with owner", e);
        }
    }

    private String generateAlertSummaryWithOwnerFromMessages(List<MessageResponse> messages) {
        Map<String, AlertInfoWithOwner> alertMap = new LinkedHashMap<>();

        // Process each message
        for (MessageResponse message : messages) {
            String messageTitle = message.getMessageTitle();
            String priority = message.getPriority();
            boolean hasReplies = message.isHasReplies();
            int numReplies = message.getReplyCount();
            String summary = message.getMessageSummary();

            String alertName = extractAlertNameFromTitle(messageTitle);
            boolean isResolved = messageTitle.contains("[RESOLVED]");

            // Determine service owner using the configuration
            String serviceOwner = instanceOwnerConfig.determineOwner(alertName, summary);

            AlertInfoWithOwner info = alertMap.getOrDefault(alertName, new AlertInfoWithOwner());
            info.alertName = alertName;
            info.occurrences++;
            info.priority = info.priority == null ? priority : info.priority;
            info.description = info.description == null ? summary : info.description;
            info.resolved = info.resolved || isResolved;
            info.hasReplies = info.hasReplies || hasReplies;
            info.totalReplies += numReplies;
            info.serviceOwner = serviceOwner;

            alertMap.put(alertName, info);
        }

        // Generate CSV content
        StringBuilder csvContent = new StringBuilder();

        // Add header with Service Owner column
        csvContent.append("\"Alert Name\",\"Occurrences\",\"Resolved_Same_Day\",\"Priority\",")
                  .append("\"Has_Replies\",\"Number_of_Replies\",\"Description\",")
                  .append("\"Alert_Types_With_Replies\",\"Max_Replies_On_Alert_Type\",\"Service Owner\"\n");

        // Add data rows
        for (AlertInfoWithOwner info : alertMap.values()) {
            csvContent.append("\"").append(escapeCSV(info.alertName)).append("\",")
                      .append("\"").append(info.occurrences).append("\",")
                      .append("\"").append(info.resolved).append("\",")
                      .append("\"").append(escapeCSV(info.priority)).append("\",")
                      .append("\"").append(info.hasReplies).append("\",")
                      .append("\"").append(info.totalReplies).append("\",")
                      .append("\"").append(escapeCSV(info.description)).append("\",")
                      .append("\"").append(info.hasReplies ? 1 : 0).append("\",")
                      .append("\"").append(info.totalReplies).append("\",")
                      .append("\"").append(escapeCSV(info.serviceOwner)).append("\"\n");
        }

        return csvContent.toString();
    }

    // Inner class for alert information with service owner
    private static class AlertInfoWithOwner {
        String alertName;
        String priority;
        String description;
        String serviceOwner;
        int occurrences = 0;
        boolean resolved = false;
        boolean hasReplies = false;
        int totalReplies = 0;
    }

    private long convertDateToTimestamp(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDateTime dateTime = date.atStartOfDay();
            return dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date format. Use YYYY-MM-DD", e);
        }
    }
}
