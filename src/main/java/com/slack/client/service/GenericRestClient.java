package com.slack.client.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Service("GenericRestClient")
public class GenericRestClient {
    
    private static final Logger logger = LoggerFactory.getLogger(GenericRestClient.class);
    
    private final RestTemplate restTemplate;
    
    public GenericRestClient() {
        this.restTemplate = new RestTemplate();
    }
    
    public <T> ResponseEntity<T> postWithHeader(String url, Object requestBody, 
                                               HttpHeaders headers, Class<T> responseType) {
        try {
            HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);
            
            logger.info("Making POST request to URL: {}", url);
            logger.debug("Request body: {}", requestBody);
            logger.debug("Headers: {}", headers);
            
            ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.POST, entity, responseType);
            
            logger.info("Response status: {}", response.getStatusCode());
            logger.debug("Response body: {}", response.getBody());
            
            return response;
            
        } catch (RestClientException e) {
            logger.error("Error making POST request to {}: {}", url, e.getMessage());
            throw new RuntimeException("REST client error: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected error making POST request to {}: {}", url, e.getMessage());
            throw new RuntimeException("Unexpected error: " + e.getMessage(), e);
        }
    }
    
    public <T> ResponseEntity<T> get(String url, Class<T> responseType) {
        try {
            logger.info("Making GET request to URL: {}", url);
            
            ResponseEntity<T> response = restTemplate.getForEntity(url, responseType);
            
            logger.info("Response status: {}", response.getStatusCode());
            logger.debug("Response body: {}", response.getBody());
            
            return response;
            
        } catch (RestClientException e) {
            logger.error("Error making GET request to {}: {}", url, e.getMessage());
            throw new RuntimeException("REST client error: " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected error making GET request to {}: {}", url, e.getMessage());
            throw new RuntimeException("Unexpected error: " + e.getMessage(), e);
        }
    }
}
