package com.slack.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

public class MessageResponse {

    @JsonProperty("timestamp")
    private String timestamp;

    @JsonProperty("message_title")
    private String messageTitle;

    @JsonProperty("message_summary")
    private String messageSummary;

    @JsonProperty("message_description")
    private String messageDescription;

    @JsonProperty("priority")
    private String priority;

    @JsonProperty("has_replies")
    private boolean hasReplies;

    @JsonProperty("reply_count")
    private int replyCount;

    @JsonProperty("sender_user_id")
    private String senderUserId;

    @JsonProperty("sender_username")
    private String senderUsername;

    @JsonProperty("channel_id")
    private String channelId;

    @JsonProperty("message_type")
    private String messageType;

    // Constructors
    public MessageResponse() {}

    public MessageResponse(String timestamp, String messageTitle, String messageSummary, String messageDescription,
                          String priority, boolean hasReplies, int replyCount,
                          String senderUserId, String senderUsername, String channelId, String messageType) {
        this.timestamp = timestamp;
        this.messageTitle = messageTitle;
        this.messageSummary = messageSummary;
        this.messageDescription = messageDescription;
        this.priority = priority;
        this.hasReplies = hasReplies;
        this.replyCount = replyCount;
        this.senderUserId = senderUserId;
        this.senderUsername = senderUsername;
        this.channelId = channelId;
        this.messageType = messageType;
    }

    // Utility method to convert Slack timestamp to readable format
    public static String formatSlackTimestamp(String slackTimestamp) {
        try {
            double timestamp = Double.parseDouble(slackTimestamp);
            Instant instant = Instant.ofEpochSecond((long) timestamp);
            LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            return dateTime.toString();
        } catch (NumberFormatException e) {
            return slackTimestamp;
        }
    }

    // Getters and Setters
    public String getTimestamp() { return timestamp; }
    public void setTimestamp(String timestamp) { this.timestamp = timestamp; }

    public String getMessageTitle() { return messageTitle; }
    public void setMessageTitle(String messageTitle) { this.messageTitle = messageTitle; }

    public String getMessageSummary() { return messageSummary; }
    public void setMessageSummary(String messageSummary) { this.messageSummary = messageSummary; }

    public String getMessageDescription() { return messageDescription; }
    public void setMessageDescription(String messageDescription) { this.messageDescription = messageDescription; }

    public String getPriority() { return priority; }
    public void setPriority(String priority) { this.priority = priority; }

    public boolean isHasReplies() { return hasReplies; }
    public void setHasReplies(boolean hasReplies) { this.hasReplies = hasReplies; }

    public int getReplyCount() { return replyCount; }
    public void setReplyCount(int replyCount) { this.replyCount = replyCount; }

    public String getSenderUserId() { return senderUserId; }
    public void setSenderUserId(String senderUserId) { this.senderUserId = senderUserId; }

    public String getSenderUsername() { return senderUsername; }
    public void setSenderUsername(String senderUsername) { this.senderUsername = senderUsername; }

    public String getChannelId() { return channelId; }
    public void setChannelId(String channelId) { this.channelId = channelId; }

    public String getMessageType() { return messageType; }
    public void setMessageType(String messageType) { this.messageType = messageType; }
}
