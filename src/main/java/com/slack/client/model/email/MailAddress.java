package com.slack.client.model.email;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MailAddress {
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("name")
    private String name;

    public MailAddress() {}

    public MailAddress(String email, String name) {
        this.email = email;
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "MailAddress{" +
                "email='" + email + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
