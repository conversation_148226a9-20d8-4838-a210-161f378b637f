package com.slack.client.model.email;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class MailRequestBody {
    @JsonProperty("from")
    private MailAddress from;
    
    @JsonProperty("reply_to")
    private String replyTo;
    
    @JsonProperty("subject")
    private String subject;
    
    @JsonProperty("content")
    private List<MailContent> content;
    
    @JsonProperty("personalizations")
    private List<MailPersonalizations> personalizations;
    
    @JsonProperty("attachments")
    private List<MailAttachment> attachments;

    public MailRequestBody() {}

    public MailRequestBody(MailAddress from, String replyTo, String subject, 
                          List<MailContent> content, List<MailPersonalizations> personalizations) {
        this.from = from;
        this.replyTo = replyTo;
        this.subject = subject;
        this.content = content;
        this.personalizations = personalizations;
    }

    public MailAddress getFrom() {
        return from;
    }

    public void setFrom(MailAddress from) {
        this.from = from;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public List<MailContent> getContent() {
        return content;
    }

    public void setContent(List<MailContent> content) {
        this.content = content;
    }

    public List<MailPersonalizations> getPersonalizations() {
        return personalizations;
    }

    public void setPersonalizations(List<MailPersonalizations> personalizations) {
        this.personalizations = personalizations;
    }

    public List<MailAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<MailAttachment> attachments) {
        this.attachments = attachments;
    }

    @Override
    public String toString() {
        return "MailRequestBody{" +
                "from=" + from +
                ", replyTo='" + replyTo + '\'' +
                ", subject='" + subject + '\'' +
                ", content=" + content +
                ", personalizations=" + personalizations +
                ", attachments=" + attachments +
                '}';
    }
}
