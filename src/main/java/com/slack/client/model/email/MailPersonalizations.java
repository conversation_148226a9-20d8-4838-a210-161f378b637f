package com.slack.client.model.email;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class MailPersonalizations {
    @JsonProperty("to")
    private List<MailAddress> to;

    public MailPersonalizations() {}

    public MailPersonalizations(List<MailAddress> to) {
        this.to = to;
    }

    public List<MailAddress> getTo() {
        return to;
    }

    public void setTo(List<MailAddress> to) {
        this.to = to;
    }

    @Override
    public String toString() {
        return "MailPersonalizations{" +
                "to=" + to +
                '}';
    }
}
