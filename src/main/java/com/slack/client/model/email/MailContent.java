package com.slack.client.model.email;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MailContent {
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("value")
    private String value;

    public MailContent() {}

    public MailContent(String type, String value) {
        this.type = type;
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "MailContent{" +
                "type='" + type + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}
