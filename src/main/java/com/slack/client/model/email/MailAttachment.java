package com.slack.client.model.email;

import com.fasterxml.jackson.annotation.JsonProperty;

public class MailAttachment {
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("content")
    private String content; // Base64 encoded content
    
    @JsonProperty("type")
    private String type;

    public MailAttachment() {}

    public MailAttachment(String name, String content, String type) {
        this.name = name;
        this.content = content;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "MailAttachment{" +
                "name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", contentLength=" + (content != null ? content.length() : 0) +
                '}';
    }
}
