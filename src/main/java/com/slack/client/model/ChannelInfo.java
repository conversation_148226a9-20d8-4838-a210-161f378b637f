package com.slack.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ChannelInfo {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("is_private")
    private boolean isPrivate;
    
    @JsonProperty("is_channel")
    private boolean isChannel;
    
    @JsonProperty("is_group")
    private boolean isGroup;
    
    @JsonProperty("is_im")
    private boolean isIm;
    
    @JsonProperty("is_mpim")
    private boolean isMpim;
    
    @JsonProperty("member_count")
    private Integer memberCount;

    // Constructors
    public ChannelInfo() {}

    public ChannelInfo(String id, String name, boolean isPrivate, boolean isChannel, 
                      boolean isGroup, boolean isIm, boolean isMpim, Integer memberCount) {
        this.id = id;
        this.name = name;
        this.isPrivate = isPrivate;
        this.isChannel = isChannel;
        this.isGroup = isGroup;
        this.isIm = isIm;
        this.isMpim = isMpim;
        this.memberCount = memberCount;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public boolean isPrivate() { return isPrivate; }
    public void setPrivate(boolean isPrivate) { this.isPrivate = isPrivate; }

    public boolean isChannel() { return isChannel; }
    public void setChannel(boolean isChannel) { this.isChannel = isChannel; }

    public boolean isGroup() { return isGroup; }
    public void setGroup(boolean isGroup) { this.isGroup = isGroup; }

    public boolean isIm() { return isIm; }
    public void setIm(boolean isIm) { this.isIm = isIm; }

    public boolean isMpim() { return isMpim; }
    public void setMpim(boolean isMpim) { this.isMpim = isMpim; }

    public Integer getMemberCount() { return memberCount; }
    public void setMemberCount(Integer memberCount) { this.memberCount = memberCount; }
}
