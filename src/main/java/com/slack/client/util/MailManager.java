package com.slack.client.util;

import com.slack.client.model.email.*;
import com.slack.client.service.GenericRestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class MailManager {

    private static final Logger logger = LoggerFactory.getLogger(MailManager.class);

    @Value("${alertSummary.mail.from.email}")
    private String fromEmail;

    @Value("${alertSummary.mail.from.name}")
    private String fromName;

    @Value("${alertSummary.mail.replyTo}")
    private String replyTo;

    @Value("${peppipostApi.url}")
    private String apiUrl;

    @Value("${peppipostApi.key}")
    private String apiKey;

    @Autowired
    @Qualifier("GenericRestClient")
    private GenericRestClient genericRestClient;

    public void sendMail(List<MailAddress> mailingList, String subject, List<MailContent> content) {
        MailRequestBody requestBody = createRequestBody(mailingList, subject, content);
        hitPeppipostEmailApi(requestBody);
    }

    public void sendMailWithAttachment(List<MailAddress> mailingList, String subject, 
                                     List<MailContent> content, MailAttachment attachment) {
        MailRequestBody requestBody = createRequestBody(mailingList, subject, content);
        requestBody.setAttachments(Collections.singletonList(attachment));
        hitPeppipostEmailApi(requestBody);
    }

    public MailRequestBody createRequestBody(List<MailAddress> mailingList, String subject, 
                                           List<MailContent> content) {
        return new MailRequestBody(new MailAddress(fromEmail, fromName), replyTo, subject, content,
                Collections.singletonList(new MailPersonalizations(mailingList)));
    }

    public void hitPeppipostEmailApi(MailRequestBody requestBody) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("api_key", apiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);

        try {
            ResponseEntity<Map> res = genericRestClient.postWithHeader(apiUrl, requestBody, headers, Map.class);
            Map<String, Object> response = res.getBody();

            if (response != null && response.containsKey("status") && 
                "success".equalsIgnoreCase(response.get("status").toString())) {
                logger.info("Email sent successfully. Response: {}", response);
            } else {
                logger.warn("Email could not be sent. Response: {}", response);
            }
        } catch (Exception e) {
            logger.error("Exception occurred in Peppipost Email API: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to send email: " + e.getMessage(), e);
        }
    }
}
