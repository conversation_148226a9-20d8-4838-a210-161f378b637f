package com.slack.client.util;

import com.opencsv.CSVWriter;
import com.slack.client.model.MessageResponse;
import org.springframework.stereotype.Component;

import java.io.StringWriter;
import java.util.List;

@Component
public class CsvExportUtil {

    public String exportMessagesToCsv(List<MessageResponse> messages) {
        StringWriter stringWriter = new StringWriter();

        try (CSVWriter csvWriter = new CSVWriter(stringWriter)) {
            // Write header
            String[] header = {
                "Timestamp",
                "Message Title",
                "Message Summary",
                "Message Description",
                "Priority",
                "Has Replies",
                "Number of Replies",
                "Sender User ID"
            };
            csvWriter.writeNext(header);

            // Write data rows
            for (MessageResponse message : messages) {
                String[] row = {
                    message.getTimestamp(),
                    cleanCsvText(message.getMessageTitle()),
                    cleanCsvText(message.getMessageSummary()),
                    cleanCsvText(message.getMessageDescription()),
                    message.getPriority(),
                    String.valueOf(message.isHasReplies()),
                    String.valueOf(message.getReplyCount()),
                    message.getSenderUserId()
                };
                csvWriter.writeNext(row);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error generating CSV", e);
        }

        return stringWriter.toString();
    }

    private String cleanCsvText(String text) {
        if (text == null) {
            return "";
        }
        // Remove newlines and excessive whitespace for CSV compatibility
        return text.replaceAll("\\r?\\n", " ")
                  .replaceAll("\\s+", " ")
                  .trim();
    }
}
