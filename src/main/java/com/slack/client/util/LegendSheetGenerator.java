package com.slack.client.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class LegendSheetGenerator {

    private static final Logger logger = LoggerFactory.getLogger(LegendSheetGenerator.class);

    public void createLegendSheet(XSSFWorkbook workbook, String startDate, String endDate) {
        try {
            logger.info("Creating Legend sheet for date range: {} to {}", startDate, endDate);
            
            // Create Legend sheet as the first sheet
            Sheet legendSheet = workbook.createSheet("Legends");
            workbook.setSheetOrder("Legends", 0);

            // Create styles
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle normalStyle = createNormalStyle(workbook);
            CellStyle orangeActionStyle = createActionStyle(workbook, IndexedColors.ORANGE);
            CellStyle lightOrangeActionStyle = createActionStyle(workbook, IndexedColors.LIGHT_ORANGE);
            CellStyle amberActionStyle = createActionStyle(workbook, IndexedColors.YELLOW);
            CellStyle greenActionStyle = createActionStyle(workbook, IndexedColors.LIGHT_GREEN);
            CellStyle skyBlueActionStyle = createActionStyle(workbook, IndexedColors.SKY_BLUE);

            int rowNum = 0;

            // Title section
            Row titleRow = legendSheet.createRow(rowNum++);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("Alert Summary Report - Legends");
            titleCell.setCellStyle(headerStyle);
            
            // Empty row
            rowNum++;

            // Date range section
            Row dateRow = legendSheet.createRow(rowNum++);
            Cell dateCell = dateRow.createCell(0);
            dateCell.setCellValue(String.format("Alert Summary From: %s To: %s", startDate, endDate));
            dateCell.setCellStyle(normalStyle);

            // Empty row
            rowNum++;

            // Action Items Legend Header
            Row legendHeaderRow = legendSheet.createRow(rowNum++);
            Cell legendHeaderCell = legendHeaderRow.createCell(0);
            legendHeaderCell.setCellValue("Action Item Color Code Meanings:");
            legendHeaderCell.setCellStyle(headerStyle);

            // Empty row
            rowNum++;

            // Legend entries
            rowNum = addLegendEntry(legendSheet, rowNum, "Actionable", orangeActionStyle, normalStyle,
                    "P0 + Unresolved + No replies - Immediate action required");

            rowNum = addLegendEntry(legendSheet, rowNum, "Actionable", lightOrangeActionStyle, normalStyle,
                    "P1 + Unresolved + No replies - Action needed");

            rowNum = addLegendEntry(legendSheet, rowNum, "Tune", amberActionStyle, normalStyle,
                    "P0 + Resolved + No replies - Tuning opportunity");

            rowNum = addLegendEntry(legendSheet, rowNum, "Review and Tune", greenActionStyle, normalStyle,
                    "P1 + Resolved + No replies - Review and tune");

            rowNum = addLegendEntry(legendSheet, rowNum, "NO ACTION", greenActionStyle, normalStyle,
                    "P1 + Resolved + Has replies - No action needed");

            rowNum = addLegendEntry(legendSheet, rowNum, "NO ACTION", lightOrangeActionStyle, normalStyle,
                    "P0 + Resolved + Has replies - No action needed");

            rowNum = addLegendEntry(legendSheet, rowNum, "Review", skyBlueActionStyle, normalStyle,
                    "Normal priority OR other cases - Routine review");

            // Auto-size columns
            for (int i = 0; i < 3; i++) {
                legendSheet.autoSizeColumn(i);
            }

            logger.info("Legend sheet created successfully with {} rows", rowNum);

        } catch (Exception e) {
            logger.error("Error creating Legend sheet: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create Legend sheet", e);
        }
    }

    private int addLegendEntry(Sheet sheet, int rowNum, String actionText, CellStyle actionStyle, 
                              CellStyle normalStyle, String description) {
        Row row = sheet.createRow(rowNum);

        // Action Item cell with color
        Cell actionCell = row.createCell(0);
        actionCell.setCellValue(actionText);
        actionCell.setCellStyle(actionStyle);

        // Separator
        Cell separatorCell = row.createCell(1);
        separatorCell.setCellValue(" - ");
        separatorCell.setCellStyle(normalStyle);

        // Description
        Cell descriptionCell = row.createCell(2);
        descriptionCell.setCellValue(description);
        descriptionCell.setCellStyle(normalStyle);

        return rowNum + 1;
    }

    private CellStyle createHeaderStyle(XSSFWorkbook workbook) {
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 14);
        headerFont.setColor(IndexedColors.WHITE.getIndex());
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        return headerStyle;
    }

    private CellStyle createNormalStyle(XSSFWorkbook workbook) {
        CellStyle normalStyle = workbook.createCellStyle();
        normalStyle.setBorderBottom(BorderStyle.THIN);
        normalStyle.setBorderTop(BorderStyle.THIN);
        normalStyle.setBorderRight(BorderStyle.THIN);
        normalStyle.setBorderLeft(BorderStyle.THIN);
        return normalStyle;
    }

    private CellStyle createActionStyle(XSSFWorkbook workbook, IndexedColors color) {
        CellStyle actionStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        actionStyle.setFont(font);
        actionStyle.setFillForegroundColor(color.getIndex());
        actionStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        actionStyle.setBorderBottom(BorderStyle.THIN);
        actionStyle.setBorderTop(BorderStyle.THIN);
        actionStyle.setBorderRight(BorderStyle.THIN);
        actionStyle.setBorderLeft(BorderStyle.THIN);
        actionStyle.setAlignment(HorizontalAlignment.CENTER);
        return actionStyle;
    }
}
