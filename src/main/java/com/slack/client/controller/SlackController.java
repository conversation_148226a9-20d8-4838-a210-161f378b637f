package com.slack.client.controller;

import com.slack.client.model.ChannelInfo;
import com.slack.client.model.MessageResponse;
import com.slack.client.service.SlackService;
import com.slack.client.util.CsvExportUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class SlackController {

    @Autowired
    private SlackService slackService;

    @Autowired
    private CsvExportUtil csvExportUtil;

    /**
     * Fetch all public and private channels
     */
    @GetMapping("/channels")
    public ResponseEntity<List<ChannelInfo>> getAllChannels() {
        try {
            List<ChannelInfo> channels = slackService.getAllChannels();
            return ResponseEntity.ok(channels);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Fetch messages from a specific channel within a date range
     */
    @GetMapping("/messages/{channelId}")
    public ResponseEntity<List<MessageResponse>> getMessagesInDateRange(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            List<MessageResponse> messages = slackService.getMessagesInDateRange(channelId, startDate, endDate, limit);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Fetch messages from a specific channel for a specific date
     */
    @GetMapping("/messages/{channelId}/date/{date}")
    public ResponseEntity<List<MessageResponse>> getMessagesForDate(
            @PathVariable String channelId,
            @PathVariable String date) {
        try {
            List<MessageResponse> messages = slackService.getMessagesForDate(channelId, date);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get count of messages in a specific channel for a specific date
     */
    @GetMapping("/messages/{channelId}/count/{date}")
    public ResponseEntity<Map<String, Object>> getMessageCountForChannelAndDate(
            @PathVariable String channelId,
            @PathVariable String date) {
        try {
            long count = slackService.getMessageCountForChannelAndDate(channelId, date);
            Map<String, Object> response = new HashMap<>();
            response.put("channel_id", channelId);
            response.put("date", date);
            response.put("message_count", count);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Fetch messages that have replies for a specific date from a channel
     */
    @GetMapping("/messages/{channelId}/replied/{date}")
    public ResponseEntity<List<MessageResponse>> getMessagesWithReplies(
            @PathVariable String channelId,
            @PathVariable String date) {
        try {
            List<MessageResponse> messages = slackService.getMessagesWithReplies(channelId, date);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Export messages in CSV format for a given date range
     */
    @GetMapping("/messages/{channelId}/csv")
    public ResponseEntity<String> exportMessagesAsCsv(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            List<MessageResponse> messages = slackService.getMessagesInDateRange(channelId, startDate, endDate, limit);
            String csvContent = csvExportUtil.exportMessagesToCsv(messages);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment",
                    String.format("slack_messages_%s_to_%s.csv", startDate, endDate));

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvContent);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate Alert Summary CSV
     */
    @GetMapping("/messages/{channelId}/summary-csv")
    public ResponseEntity<String> generateAlertSummaryCSV(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            String csvContent = slackService.generateAlertSummaryCSV(channelId, startDate, endDate, limit);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment",
                    String.format("alert_summary_%s_to_%s.csv", startDate, endDate));

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvContent);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Slack Client API");
        return ResponseEntity.ok(response);
    }
}
