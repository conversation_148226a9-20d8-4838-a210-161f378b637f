"Alert Name","Occurrences","Resolved_Same_Day","Priority","Has_Replies","Number_of_Replies","Description","Alert_Types_With_Replies","Max_Replies_On_Alert_Type"
"digital_billpayments-Saga-OMS-Consumer-lag-alerts","13","false","P1","true","3","Lag in saga oms consumer for more than 10m. Value is 5757.078125000004 on topic:","1","3"
"linux_NODE_LOW_REDIS_MEMORY ***********:9273 billpaymentsrechargesffrredisprod","17","true","P1","true","5","Node ***********:9273 of rechargesffr has had low memory over 5m - Value 6.816247559302223","1","5"
"linux_NODE_HIGH_CPU **********:9273 billpaymentsru-elkelasticsearchprod","16","true","P1","true","1","Instance **********:9273 of ru-elk has had high CPU usage Value 84.08 for over 15m","1","1"
"digital_billpayments_saga_saved_card_api_count","74","true","P1","true","22","Saga Saved API Non 2XX count increased. current value is 1.3162208234985575","1","22"
"digital_billpayments-Saga-Reminder-Consumer-lag","63","true","P1","true","29","Lag in saga Reminder consumer for more than 10m. Value is 1187.0902777777778 on topic:","1","29"
"digital_billpayments-euronetpostpaidreminder-zero-success","4","true","P0","true","2","No Success Reminder Bill Fetches from last 5 minutes. Failure Rate Current value is 99.91503522497965","1","2"
"digital_billpayments-Saga-cdc-recovery-consumer-DB-insert-reattempt-Error","22","true","P1","false","0","Saga cdc recovery consumer reattempting . Value is 725","0","0"
"CPU_HIGH_LOAD_5MIN 10.4.33.126:9273 billpaymentsupmskafkaprod","53","true","P1","true","3","Instance 10.4.33.126:9273 of upms had high cpu load for over 5m - Value 1.525","1","3"
"rechargesbff_ct_api_segments_Null","4","true","P1","true","3","Recharges-BFF ct api send failed response with http status = 2XX, sending segment id = """" to cart, count is 8519","1","3"
"digital_billpayments-4XX-dgtkt-CHENNAI digital-ticketing(metro)","2","true","P1","false","0","Count of 4XX error code  on api:  is more than threshold 10 in the 5 minutes. Value is 53","0","0"
"linux_cpu_steal_high_Dwh-fulfillment-mirror 10.4.33.188:9273 billpaymentsDwh-fulfillment-mirrorkafkaprod","72","true","P1","true","6","average cpu steal has been above 15 for 10.4.33.188:9273 of billpaymentsDwh-fulfillment-mirrorkafkaprod and Dwh-fulfillment-mirror for 10 minutes. Last Value 15.066469361187208","1","6"
"digital_billpayments-reminder-notificationService_20-consumer-kafka-lag","2","true","P1","false","0","Queue is not fully drained yet. Either draining rate may be slow or queue under high load.  Consumer Lag for topic NOTIFICATION_20 exists. Value is 193107","0","0"
"digital-catalog-client-getSeoDataAPI-4XX","9","true","P1","true","1","Get SeoData 4XX count is greater than threshold 220. Value of 4XX: 282","1","1"
"Alert Notification","5","false","Normal","true","1","Alert summary not available","1","1"
"digital_billpayments-UPI-disabled-paymode-high-count-categorywise","2","true","P1","false","0","Categorywise High Count for UPI as a Disabled Paymode compared to last week for category  electricity. Value is 136","0","0"
"linux_NODE_HIGH_CPU 10.4.32.229:9273 billpaymentsru-elkelasticsearchprod","10","true","P1","true","3","Instance 10.4.32.229:9273 of ru-elk has had high CPU usage Value 90.09 for over 15m","1","3"
"digital_billpayments-auxiliarykafka-consumer-Lag digital-billpayments-auxiliarykafka","45","true","P1","true","6","Kafka cluster digital-billpayments-auxiliarykafka Consumer Lag for topic REMINDER_MAXWELL exists. Value is 6579582.00","1","6"
"linux_NODE_HIGH_CPU 10.4.36.39:9273 billpaymentsgvtn-sagaconsumer-cache-cleanspringbootprod","2","true","P1","false","0","Instance 10.4.36.39:9273 of gvtn-sagaconsumer-cache-clean has had high CPU usage Value 75.26 for over 15m","0","0"
"CPU_HIGH_LOAD_5MIN 10.4.32.229:9273 billpaymentsru-elkelasticsearchprod","6","true","P1","true","1","Instance 10.4.32.229:9273 of ru-elk had high cpu load for over 5m - Value 1.505","1","1"
"linux_NODE_HIGH_CPU 10.4.35.118:9273 billpaymentsgvtn-sagaconsumer-cache-cleanspringbootprod","2","true","P1","false","0","Instance 10.4.35.118:9273 of gvtn-sagaconsumer-cache-clean has had high CPU usage Value 75.01 for over 15m","0","0"
"linux_NODE_HIGH_CPU 10.4.35.157:9273 billpaymentsgvtn-sagaconsumer-cache-cleanspringbootprod","2","true","P1","true","2","Instance 10.4.35.157:9273 of gvtn-sagaconsumer-cache-clean has had high CPU usage Value 75.05 for over 15m","1","2"
"linux_NODE_HIGH_CPU 10.4.35.252:9273 billpaymentsgvtn-sagaconsumer-cache-cleanspringbootprod","2","true","P1","true","1","Instance 10.4.35.252:9273 of gvtn-sagaconsumer-cache-clean has had high CPU usage Value 75.33 for over 15m","1","1"
"linux_NODE_HIGH_CPU 10.4.36.203:9273 billpaymentsgvtn-sagaconsumer-cache-cleanspringbootprod","2","true","P1","true","1","Instance 10.4.36.203:9273 of gvtn-sagaconsumer-cache-clean has had high CPU usage Value 75.18 for over 15m","1","1"
"digital_billpayments-reminder-notificationService_12-consumer-kafka-lag","2","true","P1","true","1","Queue is not fully drained yet. Either draining rate may be slow or queue under high load.  Consumer Lag for topic NOTIFICATION_12 exists. Value is 171724","1","1"
"digital_billpayments-reminder-common-or-vil-pv-notification-kafka-lag","5","true","P1","true","1","Consumer Lag for topic COMMON_PV_NOTIFICATION exists. Value is 102342","1","1"
"rechargesbff fastag insurance verify api high failure rate","2","true","P0","true","3","High failure rate detected for Fastag insurance post_/v1/expressrecharge/insurance/verify API: 5.7710501419110685%","1","3"
"CPU_HIGH_LOAD_5MIN 10.4.32.233:9273 billpaymentsru-elkelasticsearchprod","9","true","P1","true","2","Instance 10.4.32.233:9273 of ru-elk had high cpu load for over 5m - Value 1.5075","1","2"
"digital_billpayments-reminder-notificationService_7-consumer-kafka-lag","4","true","P1","true","1","Queue is not fully drained yet. Either draining rate may be slow or queue under high load.  Consumer Lag for topic NOTIFICATION_7 exists. Value is 131821","1","1"
"linux_NODE_HIGH_CPU 10.4.32.233:9273 billpaymentsru-elkelasticsearchprod","12","true","P1","true","1","Instance 10.4.32.233:9273 of ru-elk has had high CPU usage Value 90.71 for over 15m","1","1"
"digital_billpayments-reminder-realtime-notification-create-kafka-lag","7","true","P1","false","0","Consumer Lag for topic REMINDER_BILL_FETCH_REALTIME exists. Value is 1.002673e+06","0","0"
"digital_billpayments-settlement-wallet-balance-cron-daytime-insufficient-balance","2","true","P0","true","4","PLEASE CONNECT  ON FOLLOWING MOBILE NUMBER IMMEDIATELY(9650755224) OR FINANCE TEAM","1","4"
"CPU_HIGH_LOAD_5MIN **********:9273 billpaymentsru-elkelasticsearchprod","2","true","P1","false","0","Instance **********:9273 of ru-elk had high cpu load for over 5m - Value 1.5325","0","0"
"digital_billpayments-error-in-ct-events","2","true","P1","false","0","Error ib publishing CT events   value is  15","0","0"
"linux_NODE_HIGH_CPU 10.4.35.172:9273 billpaymentsgvtn-renewalnodeprod","2","true","P1","true","1","Instance 10.4.35.172:9273 of gvtn-renewal has had high CPU usage Value 75.46 for over 15m","1","1"
"digital-catalog-client-APIs-success-rate-night","16","true","P1","false","0","DCAT client APIs Success Ratio during Night is less than 0.90 in last 1 minute. Value of success ratio: 0.8992805755395683","0","0"
"linux_NODE_HIGH_CPU 10.4.39.82:9273 billpaymentsgvtn-publishernodeprod","2","true","P1","false","0","Instance 10.4.39.82:9273 of gvtn-publisher has had high CPU usage Value 75.37 for over 15m","0","0"
"digital_billpayments-HighLatency-Blue","12","true","P1","true","4","Validation API Latency is more than threshold 1 minute. Value is 4258.251948051948 on gvtn-ffrbluenode-billpayments-v1-36-73. Raise to the monitoring team to check the latency at the operators end.","1","4"
"digital_billpayments-settlement-wallet-balance-insufficient-balance","2","true","P0","true","2","PLEASE CONNECT ON FOLLOWING MOBILE NUMBER IMMEDIATELY(9650755224) OR FINANCE TEAM","1","2"
"digital_billpayments-reminder-notification-create-server-count","2","true","P1","false","0","server count is less for notification create","0","0"
"digital_billpayments-notifyTxn-Count","1","true","P0","false","0","Count of Notify transaction decreased in RU. Current value is 3724","0","0"
