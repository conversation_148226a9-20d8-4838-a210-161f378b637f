"Alert Name","Occurrences","Resolved_Same_Day","Priority","Has_Replies","Number_of_Replies","Description","Alert_Types_With_Replies","Max_Replies_On_Alert_Type","Service Owner"
"[FIRING:1] linux_NODE_LOW_REDIS_MEMORY ***********:9273 billpaymentsrechargesffrredisprod","10","false","P1","true","3","Node ***********:9273 of rechargesffr has had low memory over 5m - Value 10.037158484830524","1","3","Team Harish"
"[FIRING:1] digital_billpayments-notifyTxn-Count","3","false","P0","true","6","Count of Notify transaction decreased in RU. Current value is 4396","1","6","Team Priya"
"[FIRING:1] digital_billpayments_node_disk_usage **********:9273 digitalcatalogesmasternodeprod","69","false","P1","true","5","Disk usage &gt; 75% : **********:9273 /","1","5","Team <PERSON>sh"
"[RESOLVED] digital_billpayments-Saga-Plan-Validity-Consumer-lag","3","true","P1","false","0","Lag in saga Plan Validity consumer for more than 10m. Value is 1015.0208333333334 on topic:","0","0","Team Harish"
"[FIRING:1] digital_billpayments-Saga-Plan-Validity-Consumer-lag","5","false","P1","true","1","Lag in saga Plan Validity consumer for more than 10m. Value is 1425.1041666666663 on topic:","1","1","Team Harish"
"[RESOLVED] linux_NODE_HIGH_CPU 10.4.32.229:9273 billpaymentsru-elkelasticsearchprod","7","true","P1","false","0","Instance 10.4.32.229:9273 of ru-elk has had high CPU usage Value 85.00 for over 15m","0","0","Team Pankaj"
"[FIRING:1] linux_NODE_HIGH_CPU 10.4.32.229:9273 billpaymentsru-elkelasticsearchprod","9","false","P1","true","2","Instance 10.4.32.229:9273 of ru-elk has had high CPU usage Value 82.93 for over 15m","1","2","Team Pankaj"
"[RESOLVED] digital_billpayments-4XX-dgtkt-CHENNAI digital-ticketing(metro)","1","true","P1","false","0","Count of 4XX error code  on api:  is more than threshold 10 in the 5 minutes. Value is 103","0","0","No Owner"
"[RESOLVED] rechargesbff_ct_api_segments_Null","2","true","P1","false","0","Recharges-BFF ct api send failed response with http status = 2XX, sending segment id = """" to cart, count is 8555","0","0","Team Harish"
"[FIRING:1] rechargesbff_ct_api_segments_Null","3","false","P1","true","2","Recharges-BFF ct api send failed response with http status = 2XX, sending segment id = """" to cart, count is 8550","1","2","Team Harish"
"[RESOLVED] digital_billpayments-Saga-OMS-Consumer-lag-alerts","1","true","P1","false","0","Lag in saga oms consumer for more than 10m. Value is 1022.4609374999999 on topic:","0","0","Team Harish"
"[FIRING:1] digital_billpayments-4XX-dgtkt-CHENNAI digital-ticketing(metro)","1","false","P1","true","2","Count of 4XX error code  on api:  is more than threshold 10 in the 5 minutes. Value is 53","1","2","No Owner"
"[FIRING:1] digital_billpayments-Saga-OMS-Consumer-lag-alerts","6","false","P1","true","3","Lag in saga oms consumer for more than 10m. Value is 1209.7421875000007 on topic:","1","3","Team Harish"
"[RESOLVED] digital_billpayments-no-recharge-chennaiTraffic","1","true","P0","false","0","No Recharge Traffic observed on Challan Top Operator: chennai traffic police for the last 1 hr","0","0","No Owner"
"[RESOLVED] digital-catalog-client-APIs-success-rate-day","1","true","P1","false","0","DCAT client APIs Success Ratio during Day is less than 0.95 in last 1 minute. Value of success ratio: 0.9208228150371836","0","0","Team Harish"
"[RESOLVED] digital-catalog-client-getSeoDataAPI-4XX","5","true","P1","false","0","Get SeoData 4XX count is greater than threshold 220. Value of 4XX: 2753","0","0","Team Harish"
"[FIRING:1] digital-catalog-client-APIs-success-rate-day","1","false","P1","false","0","DCAT client APIs Success Ratio during Day is less than 0.95 in last 1 minute. Value of success ratio: 0.913014481707317","0","0","Team Harish"
"[FIRING:1] digital-catalog-client-getSeoDataAPI-4XX","5","false","P1","true","3","Get SeoData 4XX count is greater than threshold 220. Value of 4XX: 436","1","3","Team Harish"
"[FIRING:1] digital_billpayments-no-recharge-chennaiTraffic","1","false","P0","true","4","No Recharge Traffic observed on Challan Top Operator: chennai traffic police for the last 1 hr","1","4","No Owner"
"[RESOLVED] digital_billpayments-Saga-Reminder-Consumer-lag","3","true","P1","false","0","Lag in saga Reminder consumer for more than 10m. Value is 4123.763888888889 on topic:","0","0","Team Arvind"
"[FIRING:1] digital_billpayments-Saga-Reminder-Consumer-lag","4","false","P1","true","1","Lag in saga Reminder consumer for more than 10m. Value is 4024.3125 on topic:","1","1","Team Arvind"
"[RESOLVED] linux_NODE_HIGH_CPU 10.4.32.233:9273 billpaymentsru-elkelasticsearchprod","3","true","P1","false","0","Instance 10.4.32.233:9273 of ru-elk has had high CPU usage Value 80.02 for over 15m","0","0","Team Pankaj"
"[FIRING:1] linux_NODE_HIGH_CPU 10.4.32.233:9273 billpaymentsru-elkelasticsearchprod","10","false","P1","true","1","Instance 10.4.32.233:9273 of ru-elk has had high CPU usage Value 81.63 for over 15m","1","1","Team Pankaj"
"Alert Notification","5","false","Normal","true","1","Alert summary not available","1","1","Team Arvind"
"[RESOLVED] digital_billpayments-PDN-CALLBACK-API-SUCCESS-rate-drop","3","true","P1","false","0","PDN Callback API Success rate drop to less than 85%. Instructions for on-call:- Please monitor the graph pattern with earlier days. Value is 83.14049586776859","0","0","No Owner"
"[FIRING:1] digital_billpayments-PDN-CALLBACK-API-SUCCESS-rate-drop","5","false","P1","true","2","PDN Callback API Success rate drop to less than 85%. Instructions for on-call:- Please monitor the graph pattern with earlier days. Value is 80.73510773130545","1","2","No Owner"
"[RESOLVED] linux_NODE_HIGH_CPU 10.4.35.115:9273 billpaymentsgvtn-sagaconsumer-cache-cleanspringbootprod","1","true","P1","false","0","Instance 10.4.35.115:9273 of gvtn-sagaconsumer-cache-clean has had high CPU usage Value 75.34 for over 15m","0","0","Team Harish"
"[FIRING:1] linux_NODE_HIGH_CPU 10.4.35.115:9273 billpaymentsgvtn-sagaconsumer-cache-cleanspringbootprod","1","false","P1","false","0","Instance 10.4.35.115:9273 of gvtn-sagaconsumer-cache-clean has had high CPU usage Value 77.86 for over 15m","0","0","Team Harish"
"[RESOLVED] SMS_Partial_To_Full_Low_Traffic_Mobile_PrePaid","1","true","P1","false","0","SMS Parsing Partial To Full Low Traffic As Compare to yesterday","0","0","Team Arvind"
"[FIRING:1] SMS_Partial_To_Full_Low_Traffic_Mobile_PrePaid","15","false","P1","true","6","SMS Parsing Partial To Full Low Traffic As Compare to yesterday","1","6","Team Arvind"
"[RESOLVED] linux_NODE_HIGH_CPU 10.4.32.10:9273 billpaymentsru-elkelasticsearchprod","1","true","P1","false","0","Instance 10.4.32.10:9273 of ru-elk has had high CPU usage Value 80.22 for over 15m","0","0","Team Pankaj"
"[FIRING:1] linux_NODE_HIGH_CPU 10.4.32.10:9273 billpaymentsru-elkelasticsearchprod","1","false","P1","true","1","Instance 10.4.32.10:9273 of ru-elk has had high CPU usage Value 81.07 for over 15m","1","1","Team Pankaj"
"[RESOLVED] digital_billpayments-reminder-common-or-vil-pv-notification-kafka-lag","1","true","P1","false","0","Consumer Lag for topic COMMON_PV_NOTIFICATION exists. Value is 109536","0","0","Team Arvind"
"[FIRING:1] digital_billpayments-reminder-common-or-vil-pv-notification-kafka-lag","1","false","P1","false","0","Consumer Lag for topic COMMON_PV_NOTIFICATION exists. Value is 141396","0","0","Team Arvind"
"[RESOLVED] CPU_HIGH_LOAD_5MIN 10.4.32.229:9273 billpaymentsru-elkelasticsearchprod","4","true","P1","false","0","Instance 10.4.32.229:9273 of ru-elk had high cpu load for over 5m - Value 1.515","0","0","Team Pankaj"
"[FIRING:1] CPU_HIGH_LOAD_5MIN 10.4.32.229:9273 billpaymentsru-elkelasticsearchprod","4","false","P1","false","0","Instance 10.4.32.229:9273 of ru-elk had high cpu load for over 5m - Value 1.5225","0","0","Team Pankaj"
"[RESOLVED] digital_billpayments-reminder-notificationService_7-consumer-kafka-lag","1","true","P1","false","0","Queue is not fully drained yet. Either draining rate may be slow or queue under high load.  Consumer Lag for topic NOTIFICATION_7 exists. Value is 150569","0","0","Team Arvind"
"[FIRING:1] digital_billpayments-reminder-notificationService_7-consumer-kafka-lag","4","false","P1","false","0","Queue is not fully drained yet. Either draining rate may be slow or queue under high load.  Consumer Lag for topic NOTIFICATION_7 exists. Value is 725034","0","0","Team Arvind"
"[RESOLVED] digital_billpayments-reminder-common-pv-consumer","2","true","P1","false","0","Service is planValiditySubscriber, output is dropped, either latencies have increased for the service or rejection has increased, count is 363","0","0","Team Arvind"
"[RESOLVED] digital_billpayments-UPI-disabled-paymode-P1","1","true","P1","false","0","UPI Disabled Paymode High Count per Category","0","0","No Owner"
"[FIRING:1] digital_billpayments-reminder-common-pv-consumer","2","false","P1","false","0","Service is planValiditySubscriber, output is dropped, either latencies have increased for the service or rejection has increased, count is 15074","0","0","Team Arvind"
"[FIRING:1] digital_billpayments-UPI-disabled-paymode-P1","2","false","P1","true","5","UPI Disabled Paymode High Count per Category","1","5","No Owner"
"[RESOLVED] digital_billpayments-notifyTxn-Count","2","true","P0","false","0","Count of Notify transaction decreased in RU. Current value is 7974","0","0","Team Priya"
"[RESOLVED] CPU_HIGH_LOAD_5MIN 10.4.32.233:9273 billpaymentsru-elkelasticsearchprod","5","true","P1","false","0","Instance 10.4.32.233:9273 of ru-elk had high cpu load for over 5m - Value 1.5025","0","0","Team Pankaj"
"[FIRING:1] CPU_HIGH_LOAD_5MIN 10.4.32.233:9273 billpaymentsru-elkelasticsearchprod","7","false","P1","false","0","Instance 10.4.32.233:9273 of ru-elk had high cpu load for over 5m - Value 1.565","0","0","Team Pankaj"
"[RESOLVED] digital_billpayments-RENT-Biller-Service-validateBiller-API-Account-Success-Rate-Drop-Bank-P0","4","true","P0","false","0","Biller Service validateBiller API Success Rate for Account is lower than threshold 60 in last 5 minutes. Current value is 47.***************","0","0","Team Priya"
"[FIRING:1] digital_billpayments-RENT-Biller-Service-validateBiller-API-Account-Success-Rate-Drop-Bank-P0","4","false","P0","true","11","Biller Service validateBiller API Success Rate for Account is lower than threshold 60 in last 5 minutes. Current value is 50","1","11","Team Priya"
"[RESOLVED] digital_billpayments-highLatency-dgtkt-delhi--mainline-api digital-ticketing(metro)","1","true","P2","false","0","Latency of DelhiMetro Mainline API: /v1/mumbaimetro/price is more than threshold 20000 ms in the last 5m. Value is 25077.************","0","0","No Owner"
"[FIRING:1] digital_billpayments-highLatency-dgtkt-delhi--mainline-api digital-ticketing(metro)","1","false","P2","false","0","Latency of DelhiMetro Mainline API: /v1/mumbaimetro/price is more than threshold 20000 ms in the last 5m. Value is 25077.************","0","0","No Owner"
"[RESOLVED] CPU_HIGH_LOAD_5MIN 10.4.33.117:9273 billpaymentssmartreminderaerospikeprod","1","true","P1","false","0","Instance 10.4.33.117:9273 of smartreminder had high cpu load for over 5m - Value 1.50375","0","0","Team Arvind"
"[RESOLVED] CPU_HIGH_LOAD_5MIN 10.4.33.165:9273 billpaymentssmartreminderaerospikeprod","1","true","P1","false","0","Instance 10.4.33.165:9273 of smartreminder had high cpu load for over 5m - Value 1.5525","0","0","Team Arvind"
"[RESOLVED] CPU_HIGH_LOAD_5MIN 10.4.33.248:9273 billpaymentssmartreminderaerospikeprod","1","true","P1","false","0","Instance 10.4.33.248:9273 of smartreminder had high cpu load for over 5m - Value 1.58","0","0","Team Arvind"
"[RESOLVED] linux_NODE_HIGH_CPU 10.4.33.117:9273 billpaymentssmartreminderaerospikeprod","1","true","P1","false","0","Instance 10.4.33.117:9273 of smartreminder has had high CPU usage Value 86.10 for over 15m","0","0","Team Arvind"
"[RESOLVED] linux_NODE_HIGH_CPU 10.4.33.165:9273 billpaymentssmartreminderaerospikeprod","1","true","P1","false","0","Instance 10.4.33.165:9273 of smartreminder has had high CPU usage Value 85.25 for over 15m","0","0","Team Arvind"
"[RESOLVED] linux_NODE_HIGH_CPU 10.4.33.248:9273 billpaymentssmartreminderaerospikeprod","1","true","P1","false","0","Instance 10.4.33.248:9273 of smartreminder has had high CPU usage Value 85.37 for over 15m","0","0","Team Arvind"
"[FIRING:1] CPU_HIGH_LOAD_5MIN 10.4.33.117:9273 billpaymentssmartreminderaerospikeprod","3","false","P1","true","3","Instance 10.4.33.117:9273 of smartreminder had high cpu load for over 5m - Value 2.5975","1","3","Team Arvind"
"[FIRING:1] linux_NODE_HIGH_CPU 10.4.33.248:9273 billpaymentssmartreminderaerospikeprod","2","false","P1","false","0","Instance 10.4.33.248:9273 of smartreminder has had high CPU usage Value 87.71 for over 15m","0","0","Team Arvind"
"[FIRING:1] CPU_HIGH_LOAD_5MIN 10.4.33.165:9273 billpaymentssmartreminderaerospikeprod","3","false","P1","false","0","Instance 10.4.33.165:9273 of smartreminder had high cpu load for over 5m - Value 2.73625","0","0","Team Arvind"
"[FIRING:1] CPU_HIGH_LOAD_5MIN 10.4.33.248:9273 billpaymentssmartreminderaerospikeprod","3","false","P1","false","0","Instance 10.4.33.248:9273 of smartreminder had high cpu load for over 5m - Value 2.5325","0","0","Team Arvind"
"[FIRING:1] linux_NODE_HIGH_CPU 10.4.33.165:9273 billpaymentssmartreminderaerospikeprod","2","false","P1","false","0","Instance 10.4.33.165:9273 of smartreminder has had high CPU usage Value 91.24 for over 15m","0","0","Team Arvind"
"[FIRING:1] linux_NODE_HIGH_CPU 10.4.33.117:9273 billpaymentssmartreminderaerospikeprod","2","false","P1","false","0","Instance 10.4.33.117:9273 of smartreminder has had high CPU usage Value 89.85 for over 15m","0","0","Team Arvind"
"[RESOLVED] digital_billpayments-auxiliarykafka-consumer-Lag digital-billpayments-auxiliarykafka","3","true","P1","false","0","Kafka cluster digital-billpayments-auxiliarykafka Consumer Lag for topic REMINDER_MAXWELL exists. Value is 6563613.00","0","0","Team Arvind"
"[FIRING:1] digital_billpayments-auxiliarykafka-consumer-Lag digital-billpayments-auxiliarykafka","7","false","P1","true","7","Kafka cluster digital-billpayments-auxiliarykafka Consumer Lag for topic REMINDER_MAXWELL exists. Value is 7244646.00","1","7","Team Arvind"
"[RESOLVED] digital_billpayments-bill-sync-pdn-failure-count","6","true","P1","false","0","High PDN Failure Count in the last 30 mins with threshold 200 in bill sync consumer. Value is 205","0","0","No Owner"
"[FIRING:1] digital_billpayments-bill-sync-pdn-failure-count","7","false","P1","true","2","High PDN Failure Count in the last 30 mins with threshold 200 in bill sync consumer. Value is 265","1","2","No Owner"
"[RESOLVED] digital-catalog-client-APIs-success-rate-night","6","true","P1","false","0","DCAT client APIs Success Ratio during Night is less than 0.90 in last 1 minute. Value of success ratio: 0.8976721629485936","0","0","Team Harish"
"[FIRING:1] digital-catalog-client-APIs-success-rate-night","6","false","P1","true","3","DCAT client APIs Success Ratio during Night is less than 0.90 in last 1 minute. Value of success ratio: 0.****************","1","3","Team Harish"
"[RESOLVED] digital_billpayments-reminder-SMSParsing-consumer","1","true","P1","false","0","Only 2 consumers are running.","0","0","Team Arvind"
"[RESOLVED] digitaldb_mysql_replication_lag_test **********:9273 digital_subscription","1","true","Normal","false","0","Alert summary not available","0","0","Team Harish"
"[RESOLVED] digital_billpayments-RENT-Biller-Service-validateBiller-API-Account-Success-Rate-Drop-UPI-P0","1","true","P0","false","0","Biller Service validateBiller API Success Rate for Account is lower than threshold 60 in last 5 minutes. Current value is 50","0","0","Team Priya"
"[FIRING:1] digitaldb_mysql_replication_lag_test **********:9273 digital_subscription","1","false","Normal","false","0","Alert summary not available","0","0","Team Harish"
"[FIRING:1] digital_billpayments-RENT-Biller-Service-validateBiller-API-Account-Success-Rate-Drop-UPI-P0","1","false","P0","true","2","Biller Service validateBiller API Success Rate for Account is lower than threshold 60 in last 5 minutes. Current value is 50","1","2","Team Priya"
"[FIRING:1] digital_billpayments-reminder-SMSParsing-consumer","1","false","P1","true","2","Only 2 consumers are running.","1","2","Team Arvind"
"[RESOLVED] digital_billpayments-CheckoutNotify-Ratio","4","true","P2","false","0","Checkout requests coming on Bff to recieved on Notify at FFR has a drop . Value is 30.***************","0","0","Team Harish"
"[FIRING:1] digital_billpayments-CheckoutNotify-Ratio","5","false","P2","false","0","Checkout requests coming on Bff to recieved on Notify at FFR has a drop . Value is 48.**************","0","0","Team Harish"
"[RESOLVED] digital_billpayments-reminder-recents-consumer-traffic-drop","1","true","P2","false","0","recent traffic dropped by 14282 in last 1h","0","0","Team Arvind"
"[FIRING:1] digital_billpayments-reminder-recents-consumer-traffic-drop","2","false","P2","false","0","recent traffic dropped by 14282 in last 1h","0","0","Team Arvind"
"[RESOLVED] digital_billpayments-HighLatency-Blue","2","true","P1","false","0","Validation API Latency is more than threshold 1 minute. Value is 6712.595419847326 on gvtn-ffrbluenode-billpayments-v1-35-153. Raise to the monitoring team to check the latency at the operators end.","0","0","Team Harish"
"[FIRING:1] digital_billpayments-HighLatency-Blue","1","false","P1","false","0","Validation API Latency is more than threshold 1 minute. Value is 6444.357142857143 on gvtn-ffrbluenode-billpayments-v1-35-133. Raise to the monitoring team to check the latency at the operators end.","0","0","Team Harish"
"[FIRING:7] digital_billpayments-HighLatency-Blue","2","false","P1","true","2","Validation API Latency is more than threshold 1 minute. Value is 5549.3020527859235 on gvtn-ffrbluenode-billpayments-v1-35-153. Raise to the monitoring team to check the latency at the operators end.","1","2","Team Harish"
"[FIRING:5] digital_billpayments-HighLatency-Blue","3","false","P1","false","0","Validation API Latency is more than threshold 1 minute. Value is 6283.079646017699 on gvtn-ffrbluenode-billpayments-v1-35-153. Raise to the monitoring team to check the latency at the operators end.","0","0","Team Harish"
"[FIRING:2] digital_billpayments-HighLatency-Blue","1","false","P1","false","0","Validation API Latency is more than threshold 1 minute. Value is 4130.542087542088 on gvtn-ffrbluenode-billpayments-v1-35-170. Raise to the monitoring team to check the latency at the operators end.","0","0","Team Harish"
"[FIRING:4] digital_billpayments-HighLatency-Blue","1","false","P1","false","0","Validation API Latency is more than threshold 1 minute. Value is 4054.7974683544303 on gvtn-ffrbluenode-billpayments-v1-35-112. Raise to the monitoring team to check the latency at the operators end.","0","0","Team Harish"
"[FIRING:3] digital_billpayments-HighLatency-Blue","1","false","P1","false","0","Validation API Latency is more than threshold 1 minute. Value is 4054.7974683544303 on gvtn-ffrbluenode-billpayments-v1-35-112. Raise to the monitoring team to check the latency at the operators end.","0","0","Team Harish"
"[RESOLVED] digital_billpayments-settlement-wallet-balance-insufficient-balance","1","true","P0","false","0","PLEASE CONNECT ON FOLLOWING MOBILE NUMBER IMMEDIATELY(9650755224) OR FINANCE TEAM","0","0","Team Harish"
"[FIRING:1] digital_billpayments-settlement-wallet-balance-insufficient-balance","1","false","P0","true","2","PLEASE CONNECT ON FOLLOWING MOBILE NUMBER IMMEDIATELY(9650755224) OR FINANCE TEAM","1","2","Team Harish"
"[RESOLVED] linux_NODE_LOW_REDIS_MEMORY ***********:9273 billpaymentsrechargesffrredisprod","1","true","P0","false","0","Instnace name 'prod-billpayments-rechargesffrredis-v1-v1-1b-01' with IP ***********:9273 of rechargesffr has had low memory over 5m - Value 5.5969063615016825","0","0","Team Harish"
