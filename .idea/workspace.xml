<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8aa340ea-ce8f-4b7d-a3c1-0b728a0965c6" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2y83AMl72zRIV7dkF9kHSXM7Cog" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.AlertSummaryGenerator.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "/Users/<USER>/Work/ai/slack-client",
    "settings.editor.selected.configurable": "com.augmentcode.intellij.settings"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="AlertSummaryGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.slack.client.util.AlertSummaryGenerator" />
      <module name="slack-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.slack.client.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.AlertSummaryGenerator" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8aa340ea-ce8f-4b7d-a3c1-0b728a0965c6" name="Changes" comment="" />
      <created>1749202228635</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749202228635</updated>
    </task>
    <servers />
  </component>
</project>